<?php
/**
 * Enhanced Helper Functions for Website Developer Services
 * Contains utility functions used throughout the application
 */

// Format currency
function format_currency($amount, $currency = 'INR') {
    if ($currency === 'INR') {
        return '₹' . number_format($amount, 2);
    }
    return '$' . number_format($amount, 2);
}

// Get order status badge
function get_order_status_badge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning text-dark">लंबित</span>',
        'confirmed' => '<span class="badge bg-info">पुष्ट</span>',
        'in_progress' => '<span class="badge bg-primary">प्रगति में</span>',
        'completed' => '<span class="badge bg-success">पूर्ण</span>',
        'cancelled' => '<span class="badge bg-danger">रद्द</span>',
        'on_hold' => '<span class="badge bg-secondary">रोक पर</span>'
    ];
    
    return $badges[$status] ?? '<span class="badge bg-secondary">अज्ञात</span>';
}

// Get payment status badge
function get_payment_status_badge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning text-dark">लंबित</span>',
        'paid' => '<span class="badge bg-success">भुगतान हुआ</span>',
        'failed' => '<span class="badge bg-danger">असफल</span>',
        'refunded' => '<span class="badge bg-secondary">वापस किया गया</span>',
        'partial' => '<span class="badge bg-info">आंशिक</span>'
    ];
    
    return $badges[$status] ?? '<span class="badge bg-secondary">अज्ञात</span>';
}

// Generate order number
function generate_order_number() {
    return 'WD' . date('Y') . date('m') . date('d') . rand(1000, 9999);
}

// Generate secure token
function generate_secure_token($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// Sanitize input
function sanitize_input($input) {
    if (is_array($input)) {
        return array_map('sanitize_input', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Validate email
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Validate phone number (Indian format)
function validate_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return preg_match('/^[6-9]\d{9}$/', $phone);
}

// Format phone number
function format_phone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);
    if (strlen($phone) === 10) {
        return '+91-' . substr($phone, 0, 5) . '-' . substr($phone, 5);
    }
    return $phone;
}

// Time ago function
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'अभी';
    } elseif ($time < 3600) {
        $minutes = floor($time / 60);
        return $minutes . ' मिनट पहले';
    } elseif ($time < 86400) {
        $hours = floor($time / 3600);
        return $hours . ' घंटे पहले';
    } elseif ($time < 2592000) {
        $days = floor($time / 86400);
        return $days . ' दिन पहले';
    } elseif ($time < 31536000) {
        $months = floor($time / 2592000);
        return $months . ' महीने पहले';
    } else {
        $years = floor($time / 31536000);
        return $years . ' साल पहले';
    }
}

// Get file size in human readable format
function human_filesize($bytes, $decimals = 2) {
    $size = array('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB');
    $factor = floor((strlen($bytes) - 1) / 3);
    return sprintf("%.{$decimals}f", $bytes / pow(1024, $factor)) . ' ' . @$size[$factor];
}

// Generate breadcrumb
function generate_breadcrumb($items) {
    $breadcrumb = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
    
    foreach ($items as $index => $item) {
        if ($index === count($items) - 1) {
            $breadcrumb .= '<li class="breadcrumb-item active" aria-current="page">' . $item['title'] . '</li>';
        } else {
            $breadcrumb .= '<li class="breadcrumb-item"><a href="' . $item['url'] . '">' . $item['title'] . '</a></li>';
        }
    }
    
    $breadcrumb .= '</ol></nav>';
    return $breadcrumb;
}

// Log activity
function log_activity($user_id, $action, $description, $ip_address = null) {
    global $db;
    
    if (!$ip_address) {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    try {
        $db->insert('activity_logs', [
            'user_id' => $user_id,
            'action' => $action,
            'description' => $description,
            'ip_address' => $ip_address,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        error_log('Failed to log activity: ' . $e->getMessage());
    }
}

// Send notification email
function send_notification_email($to, $subject, $message, $template = 'default') {
    // This would integrate with your email service
    // For now, we'll just log it
    error_log("Email notification: To: $to, Subject: $subject");
    return true;
}

// Generate QR code URL
function generate_qr_code($data, $size = 200) {
    return "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data=" . urlencode($data);
}

// Check if file is image
function is_image($file) {
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    return in_array($file['type'], $allowed_types);
}

// Upload file
function upload_file($file, $destination, $allowed_types = [], $max_size = 5242880) {
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'फाइल अपलोड में त्रुटि'];
    }
    
    if ($file['size'] > $max_size) {
        return ['success' => false, 'message' => 'फाइल का साइज़ बहुत बड़ा है'];
    }
    
    if (!empty($allowed_types) && !in_array($file['type'], $allowed_types)) {
        return ['success' => false, 'message' => 'फाइल का प्रकार समर्थित नहीं है'];
    }
    
    $filename = uniqid() . '_' . basename($file['name']);
    $filepath = $destination . '/' . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
    }
    
    return ['success' => false, 'message' => 'फाइल सेव करने में त्रुटि'];
}

// Create thumbnail
function create_thumbnail($source, $destination, $width = 300, $height = 300) {
    $info = getimagesize($source);
    if (!$info) return false;
    
    $mime = $info['mime'];
    
    switch ($mime) {
        case 'image/jpeg':
            $image = imagecreatefromjpeg($source);
            break;
        case 'image/png':
            $image = imagecreatefrompng($source);
            break;
        case 'image/gif':
            $image = imagecreatefromgif($source);
            break;
        default:
            return false;
    }
    
    $thumb = imagecreatetruecolor($width, $height);
    imagecopyresampled($thumb, $image, 0, 0, 0, 0, $width, $height, $info[0], $info[1]);
    
    switch ($mime) {
        case 'image/jpeg':
            imagejpeg($thumb, $destination, 90);
            break;
        case 'image/png':
            imagepng($thumb, $destination);
            break;
        case 'image/gif':
            imagegif($thumb, $destination);
            break;
    }
    
    imagedestroy($image);
    imagedestroy($thumb);
    
    return true;
}

// Get user IP address
function get_user_ip() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// Rate limiting
function check_rate_limit($identifier, $max_attempts = 5, $time_window = 300) {
    global $db;
    
    $current_time = time();
    $window_start = $current_time - $time_window;
    
    // Clean old attempts
    $db->query("DELETE FROM rate_limits WHERE created_at < ?", [$window_start]);
    
    // Count current attempts
    $attempts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM rate_limits WHERE identifier = ? AND created_at >= ?",
        [$identifier, $window_start]
    )['count'];
    
    if ($attempts >= $max_attempts) {
        return false;
    }
    
    // Log this attempt
    $db->insert('rate_limits', [
        'identifier' => $identifier,
        'created_at' => $current_time
    ]);
    
    return true;
}

// Generate slug
function generate_slug($text) {
    // Replace non-letter or digits by -
    $text = preg_replace('~[^\pL\d]+~u', '-', $text);
    
    // Transliterate
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    
    // Remove unwanted characters
    $text = preg_replace('~[^-\w]+~', '', $text);
    
    // Trim
    $text = trim($text, '-');
    
    // Remove duplicate -
    $text = preg_replace('~-+~', '-', $text);
    
    // Lowercase
    $text = strtolower($text);
    
    if (empty($text)) {
        return 'n-a';
    }
    
    return $text;
}

// Encrypt data
function encrypt_data($data, $key = null) {
    if (!$key) {
        $key = ENCRYPTION_KEY ?? 'default-key-change-this';
    }
    
    $cipher = "AES-256-CBC";
    $ivlen = openssl_cipher_iv_length($cipher);
    $iv = openssl_random_pseudo_bytes($ivlen);
    $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);
    
    return base64_encode($encrypted . '::' . $iv);
}

// Decrypt data
function decrypt_data($data, $key = null) {
    if (!$key) {
        $key = ENCRYPTION_KEY ?? 'default-key-change-this';
    }
    
    $cipher = "AES-256-CBC";
    list($encrypted_data, $iv) = explode('::', base64_decode($data), 2);
    
    return openssl_decrypt($encrypted_data, $cipher, $key, 0, $iv);
}

// Get setting value
function get_setting($key, $default = null) {
    global $db;
    
    static $settings_cache = [];
    
    if (isset($settings_cache[$key])) {
        return $settings_cache[$key];
    }
    
    try {
        $setting = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = ?", [$key]);
        $value = $setting ? $setting['setting_value'] : $default;
        $settings_cache[$key] = $value;
        return $value;
    } catch (Exception $e) {
        return $default;
    }
}

// Update setting
function update_setting($key, $value) {
    global $db;
    
    try {
        $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);
        
        if ($existing) {
            $db->update('site_settings', ['setting_value' => $value], 'setting_key = ?', [$key]);
        } else {
            $db->insert('site_settings', [
                'setting_key' => $key,
                'setting_value' => $value
            ]);
        }
        
        return true;
    } catch (Exception $e) {
        error_log('Failed to update setting: ' . $e->getMessage());
        return false;
    }
}

// Check maintenance mode
function is_maintenance_mode() {
    return get_setting('maintenance_mode', false) === '1';
}

// Get system info
function get_system_info() {
    return [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'mysql_version' => 'Unknown', // Would need database connection to get this
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size')
    ];
}
?>
