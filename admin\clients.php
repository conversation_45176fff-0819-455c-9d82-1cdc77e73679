<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!is_logged_in()) {
    redirect('../login.php');
}

if (!is_admin()) {
    redirect('../client/dashboard.php');
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build WHERE clause
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $search_term = "%{$search}%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

// Only show clients (not admins)
$where_conditions[] = "role = 'client'";

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get total clients count
$total_clients = $db->fetchOne(
    "SELECT COUNT(*) as count FROM users {$where_clause}",
    $params
)['count'];

$total_pages = ceil($total_clients / $per_page);

// Get clients with order statistics
$clients = $db->fetchAll(
    "SELECT u.*, 
            COUNT(o.id) as total_orders,
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_spent,
            MAX(o.created_at) as last_order_date
     FROM users u 
     LEFT JOIN orders o ON u.id = o.user_id 
     {$where_clause}
     GROUP BY u.id
     ORDER BY u.created_at DESC 
     LIMIT {$per_page} OFFSET {$offset}",
    $params
);
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>क्लाइंट मैनेजमेंट - <?php echo SITE_NAME_HINDI; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/admin.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="../index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>डैशबोर्ड
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-1"></i>ऑर्डर
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="clients.php">
                            <i class="fas fa-users me-1"></i>क्लाइंट
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">
                            <i class="fas fa-cogs me-1"></i>सेवाएं
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i><?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../index.php">होम पेज</a></li>
                            <li><a class="dropdown-item" href="settings.php">सेटिंग्स</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">लॉगआउट</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="admin-page-header fade-in-admin">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="admin-page-title">क्लाइंट मैनेजमेंट</h1>
                    <p class="admin-page-subtitle">सभी क्लाइंट देखें और मैनेज करें</p>
                </div>
                <div>
                    <button class="admin-btn admin-btn-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                        <i class="fas fa-download me-2"></i>एक्सपोर्ट
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-number" data-counter="<?php echo $total_clients; ?>"><?php echo $total_clients; ?></div>
                    <div class="stats-label">कुल क्लाइंट</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card success fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stats-number" data-counter="<?php echo count(array_filter($clients, function($c) { return $c['status'] === 'active'; })); ?>">
                        <?php echo count(array_filter($clients, function($c) { return $c['status'] === 'active'; })); ?>
                    </div>
                    <div class="stats-label">सक्रिय क्लाइंट</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card warning fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stats-number">
                        ₹<?php echo number_format(array_sum(array_column($clients, 'total_spent'))); ?>
                    </div>
                    <div class="stats-label">कुल रेवेन्यू</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card info fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stats-number">
                        <?php echo array_sum(array_column($clients, 'total_orders')); ?>
                    </div>
                    <div class="stats-label">कुल ऑर्डर</div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="admin-card slide-in-admin">
                    <div class="card-header">
                        <h5 class="mb-0">फिल्टर और खोज</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="admin-form-label">स्टेटस फिल्टर</label>
                                <select name="status" class="form-select admin-form-control">
                                    <option value="">सभी स्टेटस</option>
                                    <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>सक्रिय</option>
                                    <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>निष्क्रिय</option>
                                    <option value="suspended" <?php echo $status_filter === 'suspended' ? 'selected' : ''; ?>>निलंबित</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="admin-form-label">खोजें</label>
                                <input type="text" name="search" class="form-control admin-form-control" 
                                       placeholder="नाम, ईमेल या फोन नंबर..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="admin-form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="admin-btn admin-btn-primary">
                                        <i class="fas fa-search me-2"></i>खोजें
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Clients Table -->
        <div class="row">
            <div class="col-12">
                <div class="admin-table slide-in-admin">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">क्लाइंट सूची (कुल: <?php echo $total_clients; ?>)</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="exportClients('excel')">
                                <i class="fas fa-file-excel me-1"></i>Excel
                            </button>
                            <button class="btn btn-outline-primary" onclick="exportClients('pdf')">
                                <i class="fas fa-file-pdf me-1"></i>PDF
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($clients)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">कोई क्लाइंट नहीं मिला</h5>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="clientsTable">
                                <thead>
                                    <tr>
                                        <th>क्लाइंट</th>
                                        <th>संपर्क</th>
                                        <th>ऑर्डर</th>
                                        <th>कुल खर्च</th>
                                        <th>अंतिम ऑर्डर</th>
                                        <th>स्टेटस</th>
                                        <th>एक्शन</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($clients as $client): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-3">
                                                    <?php echo strtoupper(substr($client['full_name'], 0, 2)); ?>
                                                </div>
                                                <div>
                                                    <strong><?php echo $client['full_name']; ?></strong><br>
                                                    <small class="text-muted">ID: <?php echo $client['id']; ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                <i class="fas fa-envelope me-1 text-primary"></i><?php echo $client['email']; ?><br>
                                                <?php if ($client['phone']): ?>
                                                <i class="fas fa-phone me-1 text-success"></i><?php echo $client['phone']; ?>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $client['total_orders']; ?> ऑर्डर</span>
                                        </td>
                                        <td>
                                            <strong class="text-success">₹<?php echo number_format($client['total_spent']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($client['last_order_date']): ?>
                                                <?php echo date('d/m/Y', strtotime($client['last_order_date'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">कोई ऑर्डर नहीं</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php 
                                            $status_class = $client['status'] === 'active' ? 'success' : 
                                                          ($client['status'] === 'inactive' ? 'secondary' : 'danger');
                                            $status_text = $client['status'] === 'active' ? 'सक्रिय' : 
                                                         ($client['status'] === 'inactive' ? 'निष्क्रिय' : 'निलंबित');
                                            ?>
                                            <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" 
                                                        onclick="viewClient(<?php echo $client['id']; ?>)"
                                                        title="विवरण देखें">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" 
                                                        onclick="contactClient('<?php echo $client['email']; ?>')"
                                                        title="संपर्क करें">
                                                    <i class="fas fa-envelope"></i>
                                                </button>
                                                <button class="btn btn-outline-warning" 
                                                        onclick="editClient(<?php echo $client['id']; ?>)"
                                                        title="संपादित करें">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                        <div class="card-footer">
                            <nav aria-label="Clients pagination">
                                <ul class="pagination justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page-1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                            पिछला
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page+1; ?>&status=<?php echo $status_filter; ?>&search=<?php echo urlencode($search); ?>">
                                            अगला
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        // Initialize DataTable
        $(document).ready(function() {
            $('#clientsTable').DataTable({
                "paging": false,
                "searching": false,
                "info": false,
                "ordering": true,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.13.6/i18n/hi.json"
                }
            });
            
            // Initialize counter animations
            animateCounters();
        });

        function viewClient(clientId) {
            window.location.href = 'client-details.php?id=' + clientId;
        }

        function contactClient(email) {
            window.location.href = 'mailto:' + email;
        }

        function editClient(clientId) {
            // Implement edit functionality
            console.log('Edit client:', clientId);
        }

        function exportClients(format) {
            // Implement export functionality
            console.log('Export clients as:', format);
        }

        function animateCounters() {
            const counters = document.querySelectorAll('[data-counter]');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-counter'));
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target.toLocaleString('hi-IN');
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current).toLocaleString('hi-IN');
                    }
                }, 20);
            });
        }
    </script>
    
    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--admin-primary), #0056b3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }
    </style>
</body>
</html>
