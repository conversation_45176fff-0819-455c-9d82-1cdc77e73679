<?php
/**
 * Security Class
 * Handles security features including reCAPTCHA, validation, and protection
 */

class Security {
    private $recaptcha_secret;
    
    public function __construct() {
        $this->recaptcha_secret = RECAPTCHA_SECRET_KEY;
    }
    
    /**
     * Verify Google reCAPTCHA
     */
    public function verifyRecaptcha($recaptcha_response, $user_ip = null) {
        if (empty($this->recaptcha_secret) || empty($recaptcha_response)) {
            return false;
        }
        
        $user_ip = $user_ip ?: $_SERVER['REMOTE_ADDR'];
        
        $data = [
            'secret' => $this->recaptcha_secret,
            'response' => $recaptcha_response,
            'remoteip' => $user_ip
        ];
        
        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data)
            ]
        ];
        
        $context = stream_context_create($options);
        $result = file_get_contents('https://www.google.com/recaptcha/api/siteverify', false, $context);
        
        if ($result === false) {
            error_log("reCAPTCHA verification failed: Unable to connect to Google");
            return false;
        }
        
        $response = json_decode($result, true);
        
        if (!$response || !isset($response['success'])) {
            error_log("reCAPTCHA verification failed: Invalid response");
            return false;
        }
        
        return $response['success'] === true;
    }
    
    /**
     * Validate email address
     */
    public function validateEmail($email) {
        if (empty($email)) {
            return false;
        }
        
        // Basic email validation
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false;
        }
        
        // Check for common disposable email domains
        $disposable_domains = [
            '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
            'mailinator.com', 'throwaway.email', 'temp-mail.org'
        ];
        
        $domain = substr(strrchr($email, "@"), 1);
        if (in_array(strtolower($domain), $disposable_domains)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate phone number (Indian format)
     */
    public function validatePhone($phone) {
        if (empty($phone)) {
            return true; // Phone is optional in most cases
        }
        
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Check Indian phone number patterns
        if (preg_match('/^(\+91|91|0)?[6-9]\d{9}$/', $phone)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Validate password strength
     */
    public function validatePassword($password) {
        $errors = [];
        
        if (strlen($password) < 6) {
            $errors[] = 'पासवर्ड कम से कम 6 अक्षर का होना चाहिए';
        }
        
        if (strlen($password) > 128) {
            $errors[] = 'पासवर्ड 128 अक्षर से अधिक नहीं हो सकता';
        }
        
        if (!preg_match('/[a-zA-Z]/', $password)) {
            $errors[] = 'पासवर्ड में कम से कम एक अक्षर होना चाहिए';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'पासवर्ड में कम से कम एक संख्या होनी चाहिए';
        }
        
        // Check for common weak passwords
        $weak_passwords = [
            'password', '123456', 'password123', 'admin', 'admin123',
            'qwerty', 'abc123', '111111', '123123', 'password1'
        ];
        
        if (in_array(strtolower($password), $weak_passwords)) {
            $errors[] = 'यह पासवर्ड बहुत सामान्य है, कृपया मजबूत पासवर्ड चुनें';
        }
        
        return empty($errors) ? true : $errors;
    }
    
    /**
     * Sanitize input data
     */
    public function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        // Remove whitespace
        $data = trim($data);
        
        // Remove backslashes
        $data = stripslashes($data);
        
        // Convert special characters to HTML entities
        $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        
        return $data;
    }
    
    /**
     * Validate file upload
     */
    public function validateFileUpload($file, $allowed_types = [], $max_size = null) {
        $errors = [];
        
        if (!isset($file['error']) || is_array($file['error'])) {
            $errors[] = 'अमान्य फ़ाइल पैरामीटर';
            return $errors;
        }
        
        // Check for upload errors
        switch ($file['error']) {
            case UPLOAD_ERR_OK:
                break;
            case UPLOAD_ERR_NO_FILE:
                $errors[] = 'कोई फ़ाइल नहीं भेजी गई';
                break;
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $errors[] = 'फ़ाइल का आकार बहुत बड़ा है';
                break;
            default:
                $errors[] = 'फ़ाइल अपलोड में त्रुटि';
                break;
        }
        
        if (!empty($errors)) {
            return $errors;
        }
        
        // Check file size
        $max_size = $max_size ?: MAX_FILE_SIZE;
        if ($file['size'] > $max_size) {
            $errors[] = 'फ़ाइल का आकार ' . $this->formatBytes($max_size) . ' से अधिक नहीं हो सकता';
        }
        
        // Check file type
        if (!empty($allowed_types)) {
            $file_info = new finfo(FILEINFO_MIME_TYPE);
            $mime_type = $file_info->file($file['tmp_name']);
            
            if (!in_array($mime_type, $allowed_types)) {
                $errors[] = 'अमान्य फ़ाइल प्रकार';
            }
        }
        
        // Check for malicious files
        $dangerous_extensions = [
            'php', 'phtml', 'php3', 'php4', 'php5', 'pl', 'py', 'jsp',
            'asp', 'sh', 'cgi', 'exe', 'bat', 'com', 'scr', 'vbs', 'js'
        ];
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (in_array($extension, $dangerous_extensions)) {
            $errors[] = 'इस प्रकार की फ़ाइल की अनुमति नहीं है';
        }
        
        return $errors;
    }
    
    /**
     * Generate secure random token
     */
    public function generateSecureToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * Hash password securely
     */
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * Verify password hash
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Rate limiting check
     */
    public function checkRateLimit($identifier, $max_attempts = 5, $time_window = 300) {
        $cache_key = "rate_limit_{$identifier}";
        
        // In a real implementation, you would use Redis or database
        // For demo, we'll use session storage
        if (!isset($_SESSION['rate_limits'])) {
            $_SESSION['rate_limits'] = [];
        }
        
        $now = time();
        
        if (!isset($_SESSION['rate_limits'][$cache_key])) {
            $_SESSION['rate_limits'][$cache_key] = [
                'attempts' => 1,
                'first_attempt' => $now
            ];
            return true;
        }
        
        $data = $_SESSION['rate_limits'][$cache_key];
        
        // Reset if time window has passed
        if ($now - $data['first_attempt'] > $time_window) {
            $_SESSION['rate_limits'][$cache_key] = [
                'attempts' => 1,
                'first_attempt' => $now
            ];
            return true;
        }
        
        // Check if limit exceeded
        if ($data['attempts'] >= $max_attempts) {
            return false;
        }
        
        // Increment attempts
        $_SESSION['rate_limits'][$cache_key]['attempts']++;
        return true;
    }
    
    /**
     * Log security event
     */
    public function logSecurityEvent($event_type, $details = '', $user_id = null) {
        global $db;
        
        try {
            $db->insert('security_logs', [
                'event_type' => $event_type,
                'details' => $details,
                'user_id' => $user_id,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Security log error: " . $e->getMessage());
        }
    }
    
    /**
     * Check for SQL injection patterns
     */
    public function detectSQLInjection($input) {
        $patterns = [
            '/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i',
            '/(\b(OR|AND)\s+\d+\s*=\s*\d+)/i',
            '/(\b(OR|AND)\s+[\'"]?\w+[\'"]?\s*=\s*[\'"]?\w+[\'"]?)/i',
            '/(--|#|\/\*|\*\/)/i',
            '/(\bSCRIPT\b)/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check for XSS patterns
     */
    public function detectXSS($input) {
        $patterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe\b/i',
            '/<object\b/i',
            '/<embed\b/i',
            '/<form\b/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Clean filename for safe storage
     */
    public function cleanFilename($filename) {
        // Remove path information
        $filename = basename($filename);
        
        // Remove special characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        
        // Remove multiple underscores
        $filename = preg_replace('/_+/', '_', $filename);
        
        // Trim underscores from start and end
        $filename = trim($filename, '_');
        
        // Ensure filename is not empty
        if (empty($filename)) {
            $filename = 'file_' . time();
        }
        
        return $filename;
    }
    
    /**
     * Generate CSRF token
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = $this->generateSecureToken();
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     */
    public function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}
?>
