<?php
/**
 * Email System Class
 * Handles all email functionality including templates and sending
 */

class EmailSystem {
    private $db;
    private $smtp_host;
    private $smtp_port;
    private $smtp_username;
    private $smtp_password;
    private $from_email;
    private $from_name;
    
    public function __construct($database) {
        $this->db = $database;
        $this->smtp_host = SMTP_HOST;
        $this->smtp_port = SMTP_PORT;
        $this->smtp_username = SMTP_USERNAME;
        $this->smtp_password = SMTP_PASSWORD;
        $this->from_email = FROM_EMAIL;
        $this->from_name = FROM_NAME;
    }
    
    /**
     * Send email using template
     */
    public function sendTemplateEmail($to_email, $template_name, $variables = []) {
        try {
            // Get email template
            $template = $this->db->fetchOne(
                "SELECT * FROM email_templates WHERE name = ? AND is_active = 1",
                [$template_name]
            );
            
            if (!$template) {
                throw new Exception("Email template '{$template_name}' not found");
            }
            
            // Use Hindi version if available
            $subject = $template['subject_hindi'] ?: $template['subject'];
            $body = $template['body_hindi'] ?: $template['body'];
            
            // Replace variables in subject and body
            foreach ($variables as $key => $value) {
                $subject = str_replace('{{' . $key . '}}', $value, $subject);
                $body = str_replace('{{' . $key . '}}', $value, $body);
            }
            
            return $this->sendEmail($to_email, $subject, $body, $template['id']);
            
        } catch (Exception $e) {
            error_log("Template email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send plain email
     */
    public function sendEmail($to_email, $subject, $body, $template_id = null) {
        try {
            // Log email attempt
            $log_id = $this->db->insert('email_logs', [
                'to_email' => $to_email,
                'subject' => $subject,
                'template_id' => $template_id,
                'status' => 'pending'
            ]);
            
            // In a real implementation, you would use PHPMailer or similar
            // For demo purposes, we'll just mark as sent
            $sent = $this->sendEmailViaSMTP($to_email, $subject, $body);
            
            // Update log status
            $this->db->update('email_logs', [
                'status' => $sent ? 'sent' : 'failed',
                'sent_at' => $sent ? date('Y-m-d H:i:s') : null,
                'error_message' => $sent ? null : 'SMTP sending failed'
            ], 'id = ?', [$log_id]);
            
            return $sent;
            
        } catch (Exception $e) {
            error_log("Email sending error: " . $e->getMessage());
            
            // Update log with error
            if (isset($log_id)) {
                $this->db->update('email_logs', [
                    'status' => 'failed',
                    'error_message' => $e->getMessage()
                ], 'id = ?', [$log_id]);
            }
            
            return false;
        }
    }
    
    /**
     * Send email via SMTP (demo implementation)
     */
    private function sendEmailViaSMTP($to_email, $subject, $body) {
        // In a real implementation, you would use PHPMailer like this:
        /*
        require_once 'vendor/autoload.php';
        
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        try {
            $mail->isSMTP();
            $mail->Host = $this->smtp_host;
            $mail->SMTPAuth = true;
            $mail->Username = $this->smtp_username;
            $mail->Password = $this->smtp_password;
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $this->smtp_port;
            $mail->CharSet = 'UTF-8';
            
            $mail->setFrom($this->from_email, $this->from_name);
            $mail->addAddress($to_email);
            
            $mail->isHTML(false);
            $mail->Subject = $subject;
            $mail->Body = $body;
            
            $mail->send();
            return true;
        } catch (Exception $e) {
            error_log("PHPMailer Error: " . $mail->ErrorInfo);
            return false;
        }
        */
        
        // For demo, just log and return true
        error_log("Demo Email - To: {$to_email}, Subject: {$subject}");
        return true;
    }
    
    /**
     * Send order confirmation email
     */
    public function sendOrderConfirmation($order_id) {
        try {
            // Get order details
            $order = $this->db->fetchOne(
                "SELECT o.*, sp.name_hindi as package_name, u.full_name, u.email 
                 FROM orders o 
                 JOIN service_packages sp ON o.package_id = sp.id 
                 JOIN users u ON o.user_id = u.id 
                 WHERE o.id = ?",
                [$order_id]
            );
            
            if (!$order) {
                throw new Exception("Order not found");
            }
            
            $variables = [
                'customer_name' => $order['full_name'],
                'order_number' => $order['order_number'],
                'package_name' => $order['package_name'],
                'amount' => format_currency($order['total_amount'])
            ];
            
            return $this->sendTemplateEmail($order['email'], 'order_confirmation', $variables);
            
        } catch (Exception $e) {
            error_log("Order confirmation email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send status update email
     */
    public function sendStatusUpdate($order_id, $new_status, $notes = '') {
        try {
            // Get order details
            $order = $this->db->fetchOne(
                "SELECT o.*, u.full_name, u.email 
                 FROM orders o 
                 JOIN users u ON o.user_id = u.id 
                 WHERE o.id = ?",
                [$order_id]
            );
            
            if (!$order) {
                throw new Exception("Order not found");
            }
            
            // Status translations
            $status_translations = [
                'pending' => 'लंबित',
                'confirmed' => 'पुष्ट',
                'in_progress' => 'प्रगति में',
                'completed' => 'पूर्ण',
                'cancelled' => 'रद्द',
                'refunded' => 'वापस'
            ];
            
            $variables = [
                'customer_name' => $order['full_name'],
                'order_number' => $order['order_number'],
                'new_status' => $status_translations[$new_status] ?? $new_status,
                'notes' => $notes
            ];
            
            return $this->sendTemplateEmail($order['email'], 'status_update', $variables);
            
        } catch (Exception $e) {
            error_log("Status update email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send contact thank you email
     */
    public function sendContactThankYou($name, $email, $message) {
        try {
            $variables = [
                'name' => $name,
                'message' => $message
            ];
            
            return $this->sendTemplateEmail($email, 'contact_thank_you', $variables);
            
        } catch (Exception $e) {
            error_log("Contact thank you email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send newsletter email
     */
    public function sendNewsletter($email_list, $subject, $content) {
        $sent_count = 0;
        $failed_count = 0;
        
        foreach ($email_list as $email) {
            if ($this->sendEmail($email, $subject, $content)) {
                $sent_count++;
            } else {
                $failed_count++;
            }
            
            // Add small delay to avoid overwhelming SMTP server
            usleep(100000); // 0.1 second delay
        }
        
        return [
            'sent' => $sent_count,
            'failed' => $failed_count,
            'total' => count($email_list)
        ];
    }
    
    /**
     * Get email statistics
     */
    public function getEmailStats($days = 30) {
        try {
            $stats = $this->db->fetchOne(
                "SELECT 
                    COUNT(*) as total_emails,
                    SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_emails,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_emails,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_emails
                 FROM email_logs 
                 WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)",
                [$days]
            );
            
            return $stats;
            
        } catch (Exception $e) {
            error_log("Email stats error: " . $e->getMessage());
            return [
                'total_emails' => 0,
                'sent_emails' => 0,
                'failed_emails' => 0,
                'pending_emails' => 0
            ];
        }
    }
    
    /**
     * Create email template
     */
    public function createTemplate($name, $subject, $subject_hindi, $body, $body_hindi, $variables = []) {
        try {
            return $this->db->insert('email_templates', [
                'name' => $name,
                'subject' => $subject,
                'subject_hindi' => $subject_hindi,
                'body' => $body,
                'body_hindi' => $body_hindi,
                'variables' => json_encode($variables),
                'is_active' => 1
            ]);
        } catch (Exception $e) {
            error_log("Create template error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update email template
     */
    public function updateTemplate($template_id, $data) {
        try {
            return $this->db->update('email_templates', $data, 'id = ?', [$template_id]);
        } catch (Exception $e) {
            error_log("Update template error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all email templates
     */
    public function getTemplates() {
        try {
            return $this->db->fetchAll(
                "SELECT * FROM email_templates ORDER BY name"
            );
        } catch (Exception $e) {
            error_log("Get templates error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get email logs with pagination
     */
    public function getEmailLogs($page = 1, $per_page = 20, $status = null) {
        try {
            $offset = ($page - 1) * $per_page;
            $where_clause = '';
            $params = [];
            
            if ($status) {
                $where_clause = 'WHERE status = ?';
                $params[] = $status;
            }
            
            $logs = $this->db->fetchAll(
                "SELECT * FROM email_logs {$where_clause} 
                 ORDER BY created_at DESC 
                 LIMIT {$per_page} OFFSET {$offset}",
                $params
            );
            
            $total = $this->db->fetchOne(
                "SELECT COUNT(*) as count FROM email_logs {$where_clause}",
                $params
            )['count'];
            
            return [
                'logs' => $logs,
                'total' => $total,
                'pages' => ceil($total / $per_page)
            ];
            
        } catch (Exception $e) {
            error_log("Get email logs error: " . $e->getMessage());
            return [
                'logs' => [],
                'total' => 0,
                'pages' => 0
            ];
        }
    }
}
?>
