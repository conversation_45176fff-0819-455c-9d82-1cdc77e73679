<?php
require_once 'config/config.php';
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>रिफंड पॉलिसी - <?php echo SITE_NAME_HINDI; ?></title>
    <meta name="description" content="हमारी रिफंड और रद्दीकरण नीति - वेब डेवलपमेंट सेवाओं के लिए रिफंड नियम">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">होम</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">सेवाएं</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="portfolio.php">पोर्टफोलियो</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">संपर्क</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main style="margin-top: 76px;">
        <div class="container py-5">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <h1 class="h3 mb-0">
                                <i class="fas fa-undo me-2"></i>रिफंड और रद्दीकरण नीति
                            </h1>
                        </div>
                        <div class="card-body p-4">
                            <p class="text-muted mb-4">
                                <strong>अंतिम अपडेट:</strong> <?php echo date('d F Y'); ?>
                            </p>

                            <div class="legal-content">
                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">1. रिफंड नीति अवलोकन</h2>
                                    <p>
                                        <?php echo SITE_NAME_HINDI; ?> ग्राहक संतुष्टि को प्राथमिकता देती है। हमारी रिफंड नीति स्पष्ट और निष्पक्ष है, जो प्रोजेक्ट की प्रगति के आधार पर रिफंड प्रदान करती है।
                                    </p>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>महत्वपूर्ण:</strong> यह नीति Razorpay की आवश्यकताओं और भारतीय उपभोक्ता संरक्षण कानून के अनुसार तैयार की गई है।
                                    </div>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">2. रिफंड योग्यता</h2>
                                    <h3 class="h5 mb-2">निम्नलिखित स्थितियों में रिफंड उपलब्ध है:</h3>
                                    <ul>
                                        <li>तकनीकी कारणों से प्रोजेक्ट पूरा नहीं हो सकता</li>
                                        <li>हमारी तरफ से समयसीमा में देरी (30 दिन से अधिक)</li>
                                        <li>सेवा की गुणवत्ता में गंभीर कमी</li>
                                        <li>क्लाइंट द्वारा प्रोजेक्ट रद्दीकरण</li>
                                        <li>पारस्परिक सहमति से प्रोजेक्ट समाप्ति</li>
                                    </ul>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">3. रिफंड स्लैब</h2>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-primary">
                                                <tr>
                                                    <th>प्रोजेक्ट स्थिति</th>
                                                    <th>रिफंड प्रतिशत</th>
                                                    <th>शर्तें</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>प्रोजेक्ट शुरू नहीं हुआ</td>
                                                    <td class="text-success fw-bold">100%</td>
                                                    <td>24 घंटे के भीतर रद्दीकरण</td>
                                                </tr>
                                                <tr>
                                                    <td>0-25% काम पूरा</td>
                                                    <td class="text-success fw-bold">75%</td>
                                                    <td>प्रारंभिक डिज़ाइन चरण</td>
                                                </tr>
                                                <tr>
                                                    <td>25-50% काम पूरा</td>
                                                    <td class="text-warning fw-bold">50%</td>
                                                    <td>डेवलपमेंट चरण</td>
                                                </tr>
                                                <tr>
                                                    <td>50-75% काम पूरा</td>
                                                    <td class="text-warning fw-bold">25%</td>
                                                    <td>टेस्टिंग चरण</td>
                                                </tr>
                                                <tr>
                                                    <td>75% से अधिक पूरा</td>
                                                    <td class="text-danger fw-bold">0%</td>
                                                    <td>फाइनल चरण</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">4. रिफंड प्रक्रिया</h2>
                                    <h3 class="h5 mb-2">रिफंड के लिए निम्नलिखित चरणों का पालन करें:</h3>
                                    <ol>
                                        <li><strong>रिफंड अनुरोध:</strong> <EMAIL> पर ईमेल भेजें</li>
                                        <li><strong>कारण बताएं:</strong> रिफंड का स्पष्ट कारण प्रदान करें</li>
                                        <li><strong>दस्तावेज़:</strong> ऑर्डर नंबर और भुगतान प्रमाण संलग्न करें</li>
                                        <li><strong>समीक्षा:</strong> हमारी टीम 2-3 कार्य दिवसों में समीक्षा करेगी</li>
                                        <li><strong>अनुमोदन:</strong> रिफंड अनुमोदन की सूचना</li>
                                        <li><strong>प्रोसेसिंग:</strong> 5-7 कार्य दिवसों में रिफंड प्रोसेसिंग</li>
                                    </ol>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">5. रिफंड समयसीमा</h2>
                                    <ul>
                                        <li><strong>रिफंड अनुरोध समीक्षा:</strong> 2-3 कार्य दिवस</li>
                                        <li><strong>रिफंड प्रोसेसिंग:</strong> 5-7 कार्य दिवस</li>
                                        <li><strong>बैंक में क्रेडिट:</strong> 3-5 कार्य दिवस</li>
                                        <li><strong>कुल समय:</strong> 10-15 कार्य दिवस</li>
                                    </ul>
                                    <p class="text-muted">
                                        <em>नोट: समयसीमा बैंक और पेमेंट गेटवे की नीतियों पर निर्भर करती है।</em>
                                    </p>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">6. रिफंड विधि</h2>
                                    <ul>
                                        <li><strong>मूल भुगतान विधि:</strong> रिफंड उसी विधि से होगा जिससे भुगतान किया गया था</li>
                                        <li><strong>कार्ड भुगतान:</strong> कार्ड में 5-7 कार्य दिवसों में क्रेडिट</li>
                                        <li><strong>UPI/नेट बैंकिंग:</strong> 3-5 कार्य दिवसों में खाते में क्रेडिट</li>
                                        <li><strong>वॉलेट:</strong> 1-3 कार्य दिवसों में वॉलेट में क्रेडिट</li>
                                    </ul>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">7. रिफंड शुल्क</h2>
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-warning">
                                                <tr>
                                                    <th>रद्दीकरण कारण</th>
                                                    <th>प्रोसेसिंग शुल्क</th>
                                                    <th>विवरण</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>हमारी तरफ से देरी</td>
                                                    <td class="text-success">कोई शुल्क नहीं</td>
                                                    <td>पूर्ण रिफंड</td>
                                                </tr>
                                                <tr>
                                                    <td>तकनीकी समस्या</td>
                                                    <td class="text-success">कोई शुल्क नहीं</td>
                                                    <td>पूर्ण रिफंड</td>
                                                </tr>
                                                <tr>
                                                    <td>क्लाइंट रद्दीकरण</td>
                                                    <td class="text-warning">5% या ₹500</td>
                                                    <td>जो भी कम हो</td>
                                                </tr>
                                                <tr>
                                                    <td>पारस्परिक सहमति</td>
                                                    <td class="text-info">3% या ₹300</td>
                                                    <td>जो भी कम हो</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">8. गैर-रिफंडेबल स्थितियां</h2>
                                    <div class="alert alert-danger">
                                        <h5 class="alert-heading">निम्नलिखित स्थितियों में रिफंड उपलब्ध नहीं है:</h5>
                                        <ul class="mb-0">
                                            <li>प्रोजेक्ट 75% से अधिक पूरा हो गया हो</li>
                                            <li>वेबसाइट लाइव हो गई हो और क्लाइंट ने स्वीकार किया हो</li>
                                            <li>क्लाइंट की तरफ से देरी के कारण प्रोजेक्ट में विलंब</li>
                                            <li>क्लाइंट द्वारा गलत या अधूरी जानकारी प्रदान करना</li>
                                            <li>तीसरे पक्ष की सेवाओं (डोमेन, होस्टिंग) की लागत</li>
                                            <li>कस्टम डेवलपमेंट में स्कोप बदलाव के बाद</li>
                                        </ul>
                                    </div>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">9. आंशिक रिफंड</h2>
                                    <p>निम्नलिखित स्थितियों में आंशिक रिफंड दिया जा सकता है:</p>
                                    <ul>
                                        <li>प्रोजेक्ट आंशिक रूप से पूरा हो गया हो</li>
                                        <li>कुछ फीचर्स डिलीवर हो गए हों</li>
                                        <li>क्लाइंट कुछ काम स्वीकार कर चुका हो</li>
                                        <li>पारस्परिक सहमति से आंशिक समाधान</li>
                                    </ul>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">10. विवाद निपटान</h2>
                                    <h3 class="h5 mb-2">रिफंड विवाद के लिए:</h3>
                                    <ol>
                                        <li><strong>प्रारंभिक चर्चा:</strong> ईमेल या फोन पर समाधान का प्रयास</li>
                                        <li><strong>एस्केलेशन:</strong> मैनेजर स्तर पर समीक्षा</li>
                                        <li><strong>मध्यस्थता:</strong> तटस्थ तीसरे पक्ष से मध्यस्थता</li>
                                        <li><strong>कानूनी कार्रवाई:</strong> अंतिम विकल्प के रूप में</li>
                                    </ol>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">11. रिफंड ट्रैकिंग</h2>
                                    <p>रिफंड की स्थिति ट्रैक करने के लिए:</p>
                                    <ul>
                                        <li>अपने क्लाइंट डैशबोर्ड में लॉगिन करें</li>
                                        <li>रिफंड स्टेटस सेक्शन देखें</li>
                                        <li>ईमेल अपडेट प्राप्त करें</li>
                                        <li>कस्टमर सपोर्ट से संपर्क करें</li>
                                    </ul>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">12. संपर्क जानकारी</h2>
                                    <div class="contact-info bg-light p-3 rounded">
                                        <h5 class="mb-3">रिफंड संबंधी सहायता के लिए:</h5>
                                        <p class="mb-1"><strong>रिफंड ईमेल:</strong> <EMAIL></p>
                                        <p class="mb-1"><strong>सपोर्ट ईमेल:</strong> <EMAIL></p>
                                        <p class="mb-1"><strong>फोन:</strong> +91-9876543210</p>
                                        <p class="mb-1"><strong>WhatsApp:</strong> +91-9876543210</p>
                                        <p class="mb-0"><strong>कार्य समय:</strong> सोमवार - शनिवार, 9:00 AM - 6:00 PM</p>
                                    </div>
                                </section>

                                <section class="mb-4">
                                    <h2 class="h4 text-primary mb-3">13. नीति अपडेट</h2>
                                    <p>
                                        हम समय-समय पर इस रिफंड नीति को अपडेट कर सकते हैं। महत्वपूर्ण बदलावों के बारे में हम आपको ईमेल या वेबसाइट नोटिस के माध्यम से सूचित करेंगे।
                                    </p>
                                </section>

                                <div class="alert alert-success">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    <strong>गारंटी:</strong> हम ग्राहक संतुष्टि को प्राथमिकता देते हैं और उचित रिफंड अनुरोधों को तुरंत प्रोसेस करते हैं।
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 <?php echo SITE_NAME_HINDI; ?>. सभी अधिकार सुरक्षित।</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="privacy-policy.php" class="text-muted text-decoration-none me-3">प्राइवेसी पॉलिसी</a>
                    <a href="terms-conditions.php" class="text-muted text-decoration-none me-3">नियम और शर्तें</a>
                    <a href="shipping-policy.php" class="text-muted text-decoration-none">शिपिंग पॉलिसी</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
