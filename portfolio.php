<?php
require_once 'config/config.php';

// Get portfolio categories
$categories = [
    'all' => 'सभी प्रोजेक्ट',
    'business' => 'बिजनेस वेबसाइट',
    'ecommerce' => 'ई-कॉमर्स',
    'custom' => 'कस्टम डेवलपमेंट'
];

$selected_category = $_GET['category'] ?? 'all';

// Sample portfolio data (in real implementation, this would come from database)
$portfolio_items = [
    [
        'id' => 1,
        'title' => 'रेस्टोरेंट बिजनेस वेबसाइट',
        'category' => 'business',
        'image' => 'assets/images/portfolio/restaurant-website.jpg',
        'description' => 'आधुनिक रेस्टोरेंट के लिए responsive वेबसाइट',
        'technologies' => ['PHP', 'MySQL', 'Bootstrap', 'JavaScript'],
        'client' => 'स्वाद रेस्टोरेंट',
        'completion_date' => '2024-01-15',
        'features' => ['ऑनलाइन मेन्यू', 'टेबल बुकिंग', 'कॉन्टैक्ट फॉर्म', 'गैलरी']
    ],
    [
        'id' => 2,
        'title' => 'फैशन ई-कॉमर्स स्टोर',
        'category' => 'ecommerce',
        'image' => 'assets/images/portfolio/fashion-ecommerce.jpg',
        'description' => 'पूर्ण ई-कॉमर्स समाधान payment gateway के साथ',
        'technologies' => ['PHP', 'MySQL', 'Razorpay', 'Bootstrap'],
        'client' => 'फैशन बाज़ार',
        'completion_date' => '2024-02-20',
        'features' => ['प्रोडक्ट कैटलॉग', 'शॉपिंग कार्ट', 'पेमेंट गेटवे', 'ऑर्डर ट्रैकिंग']
    ],
    [
        'id' => 3,
        'title' => 'एजुकेशन मैनेजमेंट सिस्टम',
        'category' => 'custom',
        'image' => 'assets/images/portfolio/education-system.jpg',
        'description' => 'स्कूल के लिए कस्टम मैनेजमेंट सिस्टम',
        'technologies' => ['PHP', 'MySQL', 'Chart.js', 'Bootstrap'],
        'client' => 'विद्या मंदिर स्कूल',
        'completion_date' => '2024-03-10',
        'features' => ['स्टूडेंट मैनेजमेंट', 'फीस ट्रैकिंग', 'रिपोर्ट जेनरेशन', 'पैरेंट पोर्टल']
    ],
    [
        'id' => 4,
        'title' => 'हेल्थकेयर क्लिनिक वेबसाइट',
        'category' => 'business',
        'image' => 'assets/images/portfolio/healthcare-website.jpg',
        'description' => 'डॉक्टर क्लिनिक के लिए appointment booking सिस्टम',
        'technologies' => ['PHP', 'MySQL', 'Calendar.js', 'Bootstrap'],
        'client' => 'केयर क्लिनिक',
        'completion_date' => '2024-01-30',
        'features' => ['अपॉइंटमेंट बुकिंग', 'डॉक्टर प्रोफाइल', 'सर्विस लिस्ट', 'कॉन्टैक्ट']
    ],
    [
        'id' => 5,
        'title' => 'ऑनलाइन ग्रॉसरी स्टोर',
        'category' => 'ecommerce',
        'image' => 'assets/images/portfolio/grocery-store.jpg',
        'description' => 'लोकल ग्रॉसरी के लिए ऑनलाइन डिलीवरी सिस्टम',
        'technologies' => ['PHP', 'MySQL', 'Google Maps', 'Razorpay'],
        'client' => 'फ्रेश मार्ट',
        'completion_date' => '2024-02-15',
        'features' => ['प्रोडक्ट सर्च', 'एरिया-वाइज डिलीवरी', 'इन्वेंटरी मैनेजमेंट', 'कस्टमर रिव्यू']
    ],
    [
        'id' => 6,
        'title' => 'रियल एस्टेट CRM सिस्टम',
        'category' => 'custom',
        'image' => 'assets/images/portfolio/real-estate-crm.jpg',
        'description' => 'प्रॉपर्टी डीलर के लिए कस्टम CRM',
        'technologies' => ['PHP', 'MySQL', 'Chart.js', 'DataTables'],
        'client' => 'प्रॉपर्टी प्लस',
        'completion_date' => '2024-03-25',
        'features' => ['लीड मैनेजमेंट', 'प्रॉपर्टी लिस्टिंग', 'क्लाइंट ट्रैकिंग', 'रिपोर्ट्स']
    ]
];

// Filter portfolio items based on category
if ($selected_category !== 'all') {
    $portfolio_items = array_filter($portfolio_items, function($item) use ($selected_category) {
        return $item['category'] === $selected_category;
    });
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>हमारा पोर्टफोलियो - <?php echo SITE_NAME_HINDI; ?></title>
    <meta name="description" content="हमारे द्वारा बनाई गई वेबसाइट और प्रोजेक्ट्स देखें - वेब डेवलपमेंट पोर्टफोलियो">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">होम</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">सेवाएं</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="portfolio.php">पोर्टफोलियो</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">हमारे बारे में</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.php">ब्लॉग</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">संपर्क</a>
                    </li>
                    <?php if (is_logged_in()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>डैशबोर्ड
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (is_admin()): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">एडमिन डैशबोर्ड</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="client/dashboard.php">क्लाइंट डैशबोर्ड</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">लॉगआउट</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">लॉगिन</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-outline-light ms-2 px-3" href="register.php">रजिस्टर करें</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-primary text-white py-5" style="margin-top: 76px;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">हमारा पोर्टफोलियो</h1>
                    <p class="lead mb-0">हमारे द्वारा बनाई गई सफल वेबसाइट और एप्लिकेशन देखें</p>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-briefcase fa-5x text-warning"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Stats -->
    <section class="py-4 bg-light">
        <div class="container">
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="text-primary fw-bold" data-counter="50">50+</h3>
                        <p class="text-muted">पूर्ण प्रोजेक्ट</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="text-success fw-bold" data-counter="45">45+</h3>
                        <p class="text-muted">खुश ग्राहक</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="text-warning fw-bold" data-counter="3">3+</h3>
                        <p class="text-muted">वर्षों का अनुभव</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <h3 class="text-info fw-bold" data-counter="15">15+</h3>
                        <p class="text-muted">तकनीकें</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Filter -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-4">
                <div class="col-12 text-center">
                    <h2 class="display-6 fw-bold mb-4">हमारे काम</h2>
                    <div class="portfolio-filter">
                        <?php foreach ($categories as $key => $name): ?>
                        <a href="portfolio.php?category=<?php echo $key; ?>" 
                           class="btn btn-outline-primary me-2 mb-2 <?php echo $selected_category === $key ? 'active' : ''; ?>">
                            <?php echo $name; ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Portfolio Grid -->
            <div class="row g-4">
                <?php foreach ($portfolio_items as $item): ?>
                <div class="col-lg-4 col-md-6">
                    <div class="portfolio-item">
                        <div class="portfolio-image">
                            <img src="<?php echo $item['image']; ?>" alt="<?php echo $item['title']; ?>" 
                                 class="img-fluid" onerror="this.src='assets/images/placeholder-portfolio.jpg'">
                            <div class="portfolio-overlay">
                                <div class="portfolio-content">
                                    <h5 class="text-white mb-2"><?php echo $item['title']; ?></h5>
                                    <p class="text-white-50 mb-3"><?php echo $item['description']; ?></p>
                                    <button class="btn btn-warning btn-sm" data-bs-toggle="modal" 
                                            data-bs-target="#portfolioModal<?php echo $item['id']; ?>">
                                        <i class="fas fa-eye me-1"></i>विवरण देखें
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="portfolio-info p-3">
                            <h6 class="mb-1"><?php echo $item['title']; ?></h6>
                            <p class="text-muted small mb-2"><?php echo $item['client']; ?></p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-primary"><?php echo $categories[$item['category']]; ?></span>
                                <small class="text-muted"><?php echo date('M Y', strtotime($item['completion_date'])); ?></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Portfolio Modal -->
                <div class="modal fade" id="portfolioModal<?php echo $item['id']; ?>" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><?php echo $item['title']; ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <img src="<?php echo $item['image']; ?>" alt="<?php echo $item['title']; ?>" 
                                     class="img-fluid mb-3 rounded" onerror="this.src='assets/images/placeholder-portfolio.jpg'">
                                
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6>प्रोजेक्ट विवरण:</h6>
                                        <p><?php echo $item['description']; ?></p>
                                        
                                        <h6>मुख्य सुविधाएं:</h6>
                                        <ul>
                                            <?php foreach ($item['features'] as $feature): ?>
                                            <li><?php echo $feature; ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>प्रोजेक्ट जानकारी:</h6>
                                        <p><strong>क्लाइंट:</strong> <?php echo $item['client']; ?></p>
                                        <p><strong>श्रेणी:</strong> <?php echo $categories[$item['category']]; ?></p>
                                        <p><strong>पूर्ण तिथि:</strong> <?php echo date('d M Y', strtotime($item['completion_date'])); ?></p>
                                        
                                        <h6>तकनीकें:</h6>
                                        <div class="mb-3">
                                            <?php foreach ($item['technologies'] as $tech): ?>
                                            <span class="badge bg-secondary me-1 mb-1"><?php echo $tech; ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">बंद करें</button>
                                <a href="contact.php" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-1"></i>ऐसा प्रोजेक्ट चाहिए
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-5 bg-primary text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h2 class="display-6 fw-bold mb-3">आपका प्रोजेक्ट यहाँ हो सकता है!</h2>
                    <p class="lead mb-0">आज ही अपना प्रोजेक्ट शुरू करें और हमारे पोर्टफोलियो का हिस्सा बनें</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <div class="d-grid gap-2 d-lg-block">
                        <a href="order.php" class="btn btn-warning btn-lg px-4">
                            <i class="fas fa-rocket me-2"></i>प्रोजेक्ट शुरू करें
                        </a>
                        <a href="contact.php" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-comments me-2"></i>चर्चा करें
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3"><?php echo SITE_NAME_HINDI; ?></h5>
                    <p class="text-muted">भारतीय बाजार के लिए पेशेवर वेब डेवलपमेंट सेवाएं।</p>
                    <div class="d-flex gap-2">
                        <a href="#" class="btn btn-outline-light btn-sm">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">सेवाएं</h6>
                    <ul class="list-unstyled">
                        <li><a href="services.php" class="text-muted text-decoration-none">वेब डेवलपमेंट</a></li>
                        <li><a href="services.php" class="text-muted text-decoration-none">ई-कॉमर्स</a></li>
                        <li><a href="services.php" class="text-muted text-decoration-none">कस्टम डेवलपमेंट</a></li>
                    </ul>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">कंपनी</h6>
                    <ul class="list-unstyled">
                        <li><a href="about.php" class="text-muted text-decoration-none">हमारे बारे में</a></li>
                        <li><a href="portfolio.php" class="text-muted text-decoration-none">पोर्टफोलियो</a></li>
                        <li><a href="blog.php" class="text-muted text-decoration-none">ब्लॉग</a></li>
                        <li><a href="contact.php" class="text-muted text-decoration-none">संपर्क</a></li>
                    </ul>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">कानूनी</h6>
                    <ul class="list-unstyled">
                        <li><a href="privacy-policy.php" class="text-muted text-decoration-none">प्राइवेसी पॉलिसी</a></li>
                        <li><a href="terms-conditions.php" class="text-muted text-decoration-none">नियम और शर्तें</a></li>
                        <li><a href="refund-policy.php" class="text-muted text-decoration-none">रिफंड पॉलिसी</a></li>
                        <li><a href="shipping-policy.php" class="text-muted text-decoration-none">डिलीवरी पॉलिसी</a></li>
                    </ul>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">संपर्क</h6>
                    <p class="text-muted mb-0">
                        <EMAIL><br>
                        +91-9876543210<br>
                        WhatsApp: +91-9876543210
                    </p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0 text-muted">&copy; 2024 <?php echo SITE_NAME_HINDI; ?>. सभी अधिकार सुरक्षित।</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <style>
        .portfolio-item {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .portfolio-item:hover {
            transform: translateY(-5px);
        }
        
        .portfolio-image {
            position: relative;
            overflow: hidden;
            height: 250px;
        }
        
        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .portfolio-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(13, 110, 253, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .portfolio-item:hover .portfolio-overlay {
            opacity: 1;
        }
        
        .portfolio-item:hover .portfolio-image img {
            transform: scale(1.1);
        }
        
        .stat-item h3 {
            font-size: 2.5rem;
        }
        
        .portfolio-filter .btn.active {
            background-color: var(--bs-primary);
            color: white;
        }
    </style>
</body>
</html>
