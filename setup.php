<?php
/**
 * Website Setup Script
 * Run this file once to set up the database and initial configuration
 */

// Prevent running in production
if (file_exists('SETUP_COMPLETE')) {
    die('Setup has already been completed. Delete SETUP_COMPLETE file to run again.');
}

$error = '';
$success = '';
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;

// Database configuration
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'webdev_services'
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step == 1) {
        // Database connection test
        $db_config['host'] = $_POST['db_host'] ?? 'localhost';
        $db_config['username'] = $_POST['db_username'] ?? 'root';
        $db_config['password'] = $_POST['db_password'] ?? '';
        $db_config['database'] = $_POST['db_database'] ?? 'webdev_services';
        
        try {
            $pdo = new PDO(
                "mysql:host={$db_config['host']}", 
                $db_config['username'], 
                $db_config['password']
            );
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            $success = 'Database connection successful!';
            $step = 2;
        } catch (PDOException $e) {
            $error = 'Database connection failed: ' . $e->getMessage();
        }
    } elseif ($step == 2) {
        // Run database schema
        try {
            $pdo = new PDO(
                "mysql:host={$db_config['host']};dbname={$db_config['database']}", 
                $db_config['username'], 
                $db_config['password']
            );
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Read and execute schema
            $schema = file_get_contents('database/schema.sql');
            $pdo->exec($schema);
            
            // Read and execute initial data
            $initial_data = file_get_contents('database/initial_data.sql');
            $pdo->exec($initial_data);
            
            $success = 'Database tables created successfully!';
            $step = 3;
        } catch (Exception $e) {
            $error = 'Database setup failed: ' . $e->getMessage();
        }
    } elseif ($step == 3) {
        // Final configuration
        $admin_password = password_hash($_POST['admin_password'], PASSWORD_DEFAULT);
        
        try {
            $pdo = new PDO(
                "mysql:host={$db_config['host']};dbname={$db_config['database']}", 
                $db_config['username'], 
                $db_config['password']
            );
            
            // Update admin password
            $stmt = $pdo->prepare("UPDATE users SET password_hash = ? WHERE username = 'admin'");
            $stmt->execute([$admin_password]);
            
            // Update settings
            $settings = [
                'site_name' => $_POST['site_name'] ?? 'Website Developer',
                'site_name_hindi' => $_POST['site_name_hindi'] ?? 'वेबसाइट डेवलपर',
                'contact_email' => $_POST['contact_email'] ?? '<EMAIL>',
                'contact_phone' => $_POST['contact_phone'] ?? '+91-9876543210',
                'whatsapp_number' => $_POST['whatsapp_number'] ?? '+91-9876543210'
            ];
            
            foreach ($settings as $key => $value) {
                $stmt = $pdo->prepare("UPDATE settings SET setting_value = ? WHERE setting_key = ?");
                $stmt->execute([$value, $key]);
            }
            
            // Create setup complete file
            file_put_contents('SETUP_COMPLETE', date('Y-m-d H:i:s'));
            
            $success = 'Setup completed successfully! You can now use your website.';
            $step = 4;
        } catch (Exception $e) {
            $error = 'Configuration failed: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Setup - वेबसाइट सेटअप</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Noto Sans Devanagari', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .setup-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-weight: bold;
        }
        .step.active {
            background: #0d6efd;
            color: white;
        }
        .step.completed {
            background: #198754;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="text-center mb-4">
                <h1 class="h2 mb-3">
                    <i class="fas fa-cog text-primary me-2"></i>
                    वेबसाइट सेटअप
                </h1>
                <p class="text-muted">अपनी वेबसाइट को सेट करने के लिए निम्नलिखित चरणों का पालन करें</p>
            </div>

            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? ($step > 3 ? 'completed' : 'active') : ''; ?>">3</div>
                <div class="step <?php echo $step >= 4 ? 'completed' : ''; ?>">4</div>
            </div>

            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
            </div>
            <?php endif; ?>

            <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
            </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
            <!-- Step 1: Database Configuration -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">चरण 1: डेटाबेस कॉन्फ़िगरेशन</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">डेटाबेस होस्ट</label>
                            <input type="text" class="form-control" name="db_host" value="localhost" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">डेटाबेस यूजरनेम</label>
                            <input type="text" class="form-control" name="db_username" value="root" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">डेटाबेस पासवर्ड</label>
                            <input type="password" class="form-control" name="db_password">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">डेटाबेस नाम</label>
                            <input type="text" class="form-control" name="db_database" value="webdev_services" required>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-arrow-right me-2"></i>अगला चरण
                        </button>
                    </form>
                </div>
            </div>

            <?php elseif ($step == 2): ?>
            <!-- Step 2: Database Setup -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">चरण 2: डेटाबेस सेटअप</h5>
                </div>
                <div class="card-body">
                    <p>डेटाबेस टेबल और प्रारंभिक डेटा बनाने के लिए नीचे बटन पर क्लिक करें।</p>
                    <form method="POST">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-database me-2"></i>डेटाबेस सेटअप करें
                        </button>
                    </form>
                </div>
            </div>

            <?php elseif ($step == 3): ?>
            <!-- Step 3: Site Configuration -->
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">चरण 3: साइट कॉन्फ़िगरेशन</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">साइट नाम (अंग्रेजी)</label>
                            <input type="text" class="form-control" name="site_name" value="Website Developer" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">साइट नाम (हिंदी)</label>
                            <input type="text" class="form-control" name="site_name_hindi" value="वेबसाइट डेवलपर" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">संपर्क ईमेल</label>
                            <input type="email" class="form-control" name="contact_email" value="<EMAIL>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">संपर्क फोन</label>
                            <input type="text" class="form-control" name="contact_phone" value="+91-9876543210" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">WhatsApp नंबर</label>
                            <input type="text" class="form-control" name="whatsapp_number" value="+91-9876543210" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">एडमिन पासवर्ड</label>
                            <input type="password" class="form-control" name="admin_password" required>
                            <div class="form-text">एडमिन अकाउंट के लिए नया पासवर्ड सेट करें</div>
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-check me-2"></i>सेटअप पूरा करें
                        </button>
                    </form>
                </div>
            </div>

            <?php elseif ($step == 4): ?>
            <!-- Step 4: Completion -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">सेटअप पूर्ण!</h5>
                </div>
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-5x text-success mb-3"></i>
                    <h4>बधाई हो! आपकी वेबसाइट तैयार है।</h4>
                    <p class="mb-4">अब आप अपनी वेबसाइट का उपयोग कर सकते हैं।</p>
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6>एडमिन लॉगिन</h6>
                                    <p class="small text-muted">ईमेल: <EMAIL></p>
                                    <a href="login.php" class="btn btn-primary btn-sm">एडमिन लॉगिन</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6>वेबसाइट देखें</h6>
                                    <p class="small text-muted">मुख्य वेबसाइट पर जाएं</p>
                                    <a href="index.php" class="btn btn-success btn-sm">होम पेज</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>अगले चरण:</h6>
                        <ul class="list-unstyled text-start">
                            <li>• Razorpay API keys सेट करें</li>
                            <li>• Google reCAPTCHA keys सेट करें</li>
                            <li>• Google Analytics ID सेट करें</li>
                            <li>• SMTP settings कॉन्फ़िगर करें</li>
                            <li>• SSL certificate इंस्टॉल करें</li>
                        </ul>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <div class="text-center mt-4">
                <small class="text-muted">
                    Website Developer Services Setup v1.0
                </small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
