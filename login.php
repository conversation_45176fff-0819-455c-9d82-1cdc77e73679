<?php
require_once 'config/config.php';

$error = '';
$success = '';

// Check if user is already logged in
if (is_logged_in()) {
    if (is_admin()) {
        redirect('admin/dashboard.php');
    } else {
        redirect('client/dashboard.php');
    }
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize_input($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!verify_csrf_token($csrf_token)) {
        $error = 'सुरक्षा त्रुटि। कृपया पुनः प्रयास करें।';
    } elseif (empty($email) || empty($password)) {
        $error = 'कृपया सभी फ़ील्ड भरें।';
    } else {
        try {
            // Get user from database
            $user = $db->fetchOne(
                "SELECT * FROM users WHERE email = ? AND status = 'active'",
                [$email]
            );
            
            if ($user && password_verify($password, $user['password_hash'])) {
                // Login successful
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_name'] = $user['full_name'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['last_activity'] = time();
                
                // Update last login
                $db->update('users', 
                    ['last_login' => date('Y-m-d H:i:s')], 
                    'id = ?', 
                    [$user['id']]
                );
                
                // Log activity
                log_activity($user['id'], 'login', 'User logged in');
                
                // Redirect based on role
                if ($user['role'] === 'admin') {
                    redirect('admin/dashboard.php');
                } else {
                    redirect('client/dashboard.php');
                }
            } else {
                $error = 'गलत ईमेल या पासवर्ड।';
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $error = 'लॉगिन में त्रुटि। कृपया बाद में प्रयास करें।';
        }
    }
}

// Check for logout message
if (isset($_GET['logout'])) {
    $success = 'आप सफलतापूर्वक लॉगआउट हो गए हैं।';
}

// Check for timeout message
if (isset($_GET['timeout'])) {
    $error = 'आपका सेशन समाप्त हो गया है। कृपया पुनः लॉगिन करें।';
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>लॉगिन - <?php echo SITE_NAME_HINDI; ?></title>
    <meta name="description" content="अपने अकाउंट में लॉगिन करें">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- reCAPTCHA -->
    <?php if (RECAPTCHA_SITE_KEY): ?>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <?php endif; ?>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">होम</a>
                <a class="nav-link" href="register.php">रजिस्टर करें</a>
            </div>
        </div>
    </nav>

    <!-- Login Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-5">
                    <div class="card shadow-lg border-0">
                        <div class="card-header bg-primary text-white text-center py-4">
                            <h3 class="mb-0">
                                <i class="fas fa-sign-in-alt me-2"></i>लॉगिन करें
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($success): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <?php endif; ?>

                            <form method="POST" action="login.php" id="loginForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>ईमेल पता
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($email ?? ''); ?>" 
                                           required placeholder="आपका ईमेल पता दर्ज करें">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>पासवर्ड
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password" name="password" 
                                               required placeholder="आपका पासवर्ड दर्ज करें">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="remember">
                                    <label class="form-check-label" for="remember">
                                        मुझे याद रखें
                                    </label>
                                </div>
                                
                                <?php if (RECAPTCHA_SITE_KEY): ?>
                                <div class="mb-3">
                                    <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>लॉगिन करें
                                    </button>
                                </div>
                            </form>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-2">
                                    <a href="forgot-password.php" class="text-decoration-none">
                                        <i class="fas fa-key me-1"></i>पासवर्ड भूल गए?
                                    </a>
                                </p>
                                <p class="mb-0">
                                    अकाउंट नहीं है? 
                                    <a href="register.php" class="text-decoration-none fw-bold">
                                        यहाँ रजिस्टर करें
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Demo Credentials -->
                    <div class="card mt-4 border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>डेमो क्रेडेंशियल
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-2"><strong>एडमिन लॉगिन:</strong></p>
                            <p class="mb-1">ईमेल: <EMAIL></p>
                            <p class="mb-0">पासवर्ड: admin123</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 <?php echo SITE_NAME_HINDI; ?>. सभी अधिकार सुरक्षित।</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="privacy.php" class="text-muted text-decoration-none me-3">प्राइवेसी पॉलिसी</a>
                    <a href="terms.php" class="text-muted text-decoration-none">नियम और शर्तें</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Form validation
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                e.preventDefault();
                alert('कृपया सभी फ़ील्ड भरें।');
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="loading me-2"></span>लॉगिन हो रहा है...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
