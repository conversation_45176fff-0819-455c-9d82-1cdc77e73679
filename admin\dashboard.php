<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!is_logged_in()) {
    redirect('../login.php');
}

if (!is_admin()) {
    redirect('../client/dashboard.php');
}

// Get dashboard statistics
$stats = $db->fetchOne(
    "SELECT 
        (SELECT COUNT(*) FROM orders) as total_orders,
        (SELECT COUNT(*) FROM orders WHERE status = 'pending') as pending_orders,
        (SELECT COUNT(*) FROM orders WHERE status = 'in_progress') as in_progress_orders,
        (SELECT COUNT(*) FROM orders WHERE status = 'completed') as completed_orders,
        (SELECT COUNT(*) FROM users WHERE role = 'client') as total_clients,
        (SELECT COUNT(*) FROM users WHERE role = 'client' AND DATE(created_at) = CURDATE()) as new_clients_today,
        (SELECT SUM(total_amount) FROM orders WHERE payment_status = 'paid') as total_revenue,
        (SELECT SUM(total_amount) FROM orders WHERE payment_status = 'paid' AND DATE(created_at) = CURDATE()) as today_revenue,
        (SELECT COUNT(*) FROM contact_inquiries WHERE status = 'new') as new_inquiries,
        (SELECT COUNT(*) FROM blog_posts WHERE status = 'published') as published_posts"
);

// Get recent orders
$recent_orders = $db->fetchAll(
    "SELECT o.*, sp.name_hindi as package_name, u.full_name as client_name 
     FROM orders o 
     JOIN service_packages sp ON o.package_id = sp.id 
     JOIN users u ON o.user_id = u.id 
     ORDER BY o.created_at DESC 
     LIMIT 5"
);

// Get recent clients
$recent_clients = $db->fetchAll(
    "SELECT * FROM users WHERE role = 'client' ORDER BY created_at DESC LIMIT 5"
);

// Get monthly revenue data for chart
$monthly_revenue = $db->fetchAll(
    "SELECT 
        DATE_FORMAT(created_at, '%Y-%m') as month,
        SUM(total_amount) as revenue
     FROM orders 
     WHERE payment_status = 'paid' 
     AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
     GROUP BY DATE_FORMAT(created_at, '%Y-%m')
     ORDER BY month"
);
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>एडमिन डैशबोर्ड - <?php echo SITE_NAME_HINDI; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="../index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>डैशबोर्ड
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-1"></i>ऑर्डर
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.php">
                            <i class="fas fa-users me-1"></i>क्लाइंट
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">
                            <i class="fas fa-cogs me-1"></i>सेवाएं
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.php">
                            <i class="fas fa-blog me-1"></i>ब्लॉग
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="inquiries.php">
                            <i class="fas fa-envelope me-1"></i>पूछताछ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar me-1"></i>रिपोर्ट
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i><?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../index.php">होम पेज</a></li>
                            <li><a class="dropdown-item" href="settings.php">सेटिंग्स</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">लॉगआउट</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-2">नमस्कार, <?php echo $_SESSION['user_name']; ?>!</h2>
                                <p class="mb-0">एडमिन डैशबोर्ड में आपका स्वागत है। यहाँ आप सभी व्यावसायिक गतिविधियों को ट्रैक कर सकते हैं।</p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="text-white-50">
                                    <i class="fas fa-calendar me-2"></i><?php echo date('d F Y'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="dashboard-card border-left-primary">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="dashboard-stat text-primary"><?php echo $stats['total_orders'] ?? 0; ?></div>
                            <div class="dashboard-label">कुल ऑर्डर</div>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="dashboard-card border-left-warning">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="dashboard-stat text-warning"><?php echo $stats['pending_orders'] ?? 0; ?></div>
                            <div class="dashboard-label">लंबित ऑर्डर</div>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="dashboard-card border-left-success">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="dashboard-stat text-success"><?php echo $stats['total_clients'] ?? 0; ?></div>
                            <div class="dashboard-label">कुल क्लाइंट</div>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="dashboard-card border-left-info">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="dashboard-stat text-info"><?php echo format_currency($stats['total_revenue'] ?? 0); ?></div>
                            <div class="dashboard-label">कुल आय</div>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-rupee-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secondary Stats -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <h4 class="text-primary"><?php echo $stats['in_progress_orders'] ?? 0; ?></h4>
                        <p class="text-muted mb-0">प्रगति में</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <h4 class="text-success"><?php echo $stats['completed_orders'] ?? 0; ?></h4>
                        <p class="text-muted mb-0">पूर्ण</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <h4 class="text-warning"><?php echo $stats['new_inquiries'] ?? 0; ?></h4>
                        <p class="text-muted mb-0">नई पूछताछ</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <h4 class="text-info"><?php echo format_currency($stats['today_revenue'] ?? 0); ?></h4>
                        <p class="text-muted mb-0">आज की आय</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Tables Row -->
        <div class="row">
            <!-- Revenue Chart -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow border-0">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>मासिक आय ट्रेंड
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="revenueChart" height="100"></canvas>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow border-0">
                    <div class="card-header bg-white border-bottom">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>त्वरित कार्य
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="orders.php?status=pending" class="btn btn-warning btn-sm">
                                <i class="fas fa-clock me-2"></i>लंबित ऑर्डर देखें
                            </a>
                            <a href="inquiries.php?status=new" class="btn btn-info btn-sm">
                                <i class="fas fa-envelope me-2"></i>नई पूछताछ देखें
                            </a>
                            <a href="blog.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-2"></i>नया ब्लॉग पोस्ट
                            </a>
                            <a href="services.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-cogs me-2"></i>सेवाएं प्रबंधित करें
                            </a>
                            <a href="reports.php" class="btn btn-secondary btn-sm">
                                <i class="fas fa-download me-2"></i>रिपोर्ट डाउनलोड करें
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders and Clients -->
        <div class="row">
            <!-- Recent Orders -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow border-0">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    <i class="fas fa-shopping-cart me-2"></i>हाल के ऑर्डर
                                </h5>
                            </div>
                            <div class="col-auto">
                                <a href="orders.php" class="btn btn-outline-primary btn-sm">सभी देखें</a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($recent_orders)): ?>
                        <div class="text-center py-4">
                            <p class="text-muted">कोई ऑर्डर नहीं मिला</p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>ऑर्डर #</th>
                                        <th>क्लाइंट</th>
                                        <th>पैकेज</th>
                                        <th>राशि</th>
                                        <th>स्थिति</th>
                                        <th>दिनांक</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_orders as $order): ?>
                                    <tr>
                                        <td><strong><?php echo $order['order_number']; ?></strong></td>
                                        <td><?php echo $order['client_name']; ?></td>
                                        <td><?php echo $order['package_name']; ?></td>
                                        <td><?php echo format_currency($order['total_amount']); ?></td>
                                        <td><?php echo get_order_status_badge($order['status']); ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($order['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Recent Clients -->
            <div class="col-lg-4 mb-4">
                <div class="card shadow border-0">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    <i class="fas fa-users me-2"></i>नए क्लाइंट
                                </h5>
                            </div>
                            <div class="col-auto">
                                <a href="clients.php" class="btn btn-outline-primary btn-sm">सभी देखें</a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_clients)): ?>
                        <p class="text-muted text-center">कोई नया क्लाइंट नहीं</p>
                        <?php else: ?>
                        <?php foreach ($recent_clients as $client): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                <?php echo strtoupper(substr($client['full_name'], 0, 1)); ?>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-0"><?php echo $client['full_name']; ?></h6>
                                <small class="text-muted"><?php echo time_ago($client['created_at']); ?></small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Revenue Chart
        const ctx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [<?php 
                    $labels = [];
                    foreach ($monthly_revenue as $data) {
                        $labels[] = "'" . date('M Y', strtotime($data['month'] . '-01')) . "'";
                    }
                    echo implode(',', $labels);
                ?>],
                datasets: [{
                    label: 'आय (₹)',
                    data: [<?php 
                        $values = [];
                        foreach ($monthly_revenue as $data) {
                            $values[] = $data['revenue'];
                        }
                        echo implode(',', $values);
                    ?>],
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Auto-refresh dashboard every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
    
    <style>
        .border-left-primary {
            border-left: 4px solid var(--bs-primary) !important;
        }
        .border-left-warning {
            border-left: 4px solid var(--bs-warning) !important;
        }
        .border-left-success {
            border-left: 4px solid var(--bs-success) !important;
        }
        .border-left-info {
            border-left: 4px solid var(--bs-info) !important;
        }
        .avatar-sm {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
    </style>
</body>
</html>
