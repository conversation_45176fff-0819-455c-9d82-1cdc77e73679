/**
 * Enhanced Main JavaScript file for Website Developer Services
 * Handles common functionality across the website with advanced features
 */

// Global variables and configuration
const WebsiteDeveloper = {
    config: {
        animationDuration: 300,
        scrollOffset: 100,
        debounceDelay: 300,
        apiEndpoint: '/api/',
        version: '2.0.0'
    },
    state: {
        isLoading: false,
        activeModals: [],
        scrollPosition: 0,
        isMobile: window.innerWidth <= 768
    },
    cache: new Map(),
    observers: {
        intersection: null,
        mutation: null,
        resize: null
    }
};

// Enhanced Document Ready with Performance Optimization
document.addEventListener('DOMContentLoaded', function() {
    // Performance timing
    const startTime = performance.now();

    // Initialize core components
    initializeComponents();
    setupEventListeners();
    setupFormValidation();
    setupAjaxDefaults();
    setupAdvancedFeatures();

    // Log initialization time
    const endTime = performance.now();
    console.log(`Website initialized in ${(endTime - startTime).toFixed(2)}ms`);
});

/**
 * Initialize all components
 */
function initializeComponents() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Setup smooth scrolling
    setupSmoothScrolling();
    
    // Setup loading states
    setupLoadingStates();
    
    // Setup auto-hide alerts
    setupAutoHideAlerts();
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Mobile menu toggle
    const navbarToggler = document.querySelector('.navbar-toggler');
    if (navbarToggler) {
        navbarToggler.addEventListener('click', function() {
            this.classList.toggle('active');
        });
    }

    // Back to top button
    setupBackToTop();
    
    // Image lazy loading fallback
    setupImageLazyLoading();
    
    // External links
    setupExternalLinks();
    
    // Copy to clipboard functionality
    setupCopyToClipboard();
}

/**
 * Setup form validation
 */
function setupFormValidation() {
    // Bootstrap form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // Custom validation rules
    setupCustomValidation();
    
    // Real-time validation
    setupRealTimeValidation();
}

/**
 * Setup AJAX defaults
 */
function setupAjaxDefaults() {
    // Add CSRF token to all AJAX requests
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        // If using jQuery (optional)
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                }
            });
        }
    }
}

/**
 * Setup smooth scrolling for anchor links
 */
function setupSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Setup loading states for buttons
 */
function setupLoadingStates() {
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn && !isLoading) {
                showButtonLoading(submitBtn);
            }
        });
    });
}

/**
 * Show loading state on button
 */
function showButtonLoading(button) {
    if (button.dataset.originalText) return; // Already loading
    
    button.dataset.originalText = button.innerHTML;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>लोड हो रहा है...';
    button.disabled = true;
    isLoading = true;
}

/**
 * Hide loading state on button
 */
function hideButtonLoading(button) {
    if (button.dataset.originalText) {
        button.innerHTML = button.dataset.originalText;
        delete button.dataset.originalText;
        button.disabled = false;
        isLoading = false;
    }
}

/**
 * Setup auto-hide alerts
 */
function setupAutoHideAlerts() {
    document.querySelectorAll('.alert[data-auto-hide]').forEach(alert => {
        const delay = parseInt(alert.dataset.autoHide) || 5000;
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, delay);
    });
}

/**
 * Setup back to top button
 */
function setupBackToTop() {
    // Create back to top button
    const backToTop = document.createElement('button');
    backToTop.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTop.className = 'btn btn-primary btn-back-to-top';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: none;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    
    document.body.appendChild(backToTop);
    
    // Show/hide on scroll
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.display = 'block';
        } else {
            backToTop.style.display = 'none';
        }
    });
    
    // Scroll to top on click
    backToTop.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

/**
 * Setup image lazy loading fallback
 */
function setupImageLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * Setup external links
 */
function setupExternalLinks() {
    document.querySelectorAll('a[href^="http"]').forEach(link => {
        if (!link.href.includes(window.location.hostname)) {
            link.target = '_blank';
            link.rel = 'noopener noreferrer';
        }
    });
}

/**
 * Setup copy to clipboard functionality
 */
function setupCopyToClipboard() {
    document.querySelectorAll('[data-copy]').forEach(element => {
        element.addEventListener('click', function() {
            const text = this.dataset.copy || this.textContent;
            navigator.clipboard.writeText(text).then(() => {
                showToast('कॉपी हो गया!', 'success');
            }).catch(() => {
                showToast('कॉपी नहीं हो सका', 'error');
            });
        });
    });
}

/**
 * Setup custom validation rules
 */
function setupCustomValidation() {
    // Phone number validation for Indian numbers
    document.querySelectorAll('input[type="tel"]').forEach(input => {
        input.addEventListener('input', function() {
            const phone = this.value.replace(/[^0-9]/g, '');
            const isValid = /^[6-9]\d{9}$/.test(phone);
            
            if (this.value && !isValid) {
                this.setCustomValidity('कृपया वैध भारतीय मोबाइल नंबर दर्ज करें');
            } else {
                this.setCustomValidity('');
            }
        });
    });

    // Email validation
    document.querySelectorAll('input[type="email"]').forEach(input => {
        input.addEventListener('input', function() {
            const email = this.value;
            const isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            
            if (this.value && !isValid) {
                this.setCustomValidity('कृपया वैध ईमेल पता दर्ज करें');
            } else {
                this.setCustomValidity('');
            }
        });
    });
}

/**
 * Setup real-time validation
 */
function setupRealTimeValidation() {
    document.querySelectorAll('input, textarea, select').forEach(field => {
        field.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        document.body.appendChild(toastContainer);
    }

    // Create toast
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

/**
 * Format currency (Indian Rupees)
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('hi-IN', {
        style: 'currency',
        currency: 'INR'
    }).format(amount);
}

/**
 * Format date in Hindi
 */
function formatDateHindi(date) {
    return new Intl.DateTimeFormat('hi-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Check if element is in viewport
 */
function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

/**
 * Animate counter
 */
function animateCounter(element, start, end, duration) {
    let startTimestamp = null;
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        const current = Math.floor(progress * (end - start) + start);
        element.textContent = current.toLocaleString('hi-IN');
        if (progress < 1) {
            window.requestAnimationFrame(step);
        }
    };
    window.requestAnimationFrame(step);
}

/**
 * Setup counter animations
 */
function setupCounterAnimations() {
    const counters = document.querySelectorAll('[data-counter]');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && !entry.target.dataset.animated) {
                const target = parseInt(entry.target.dataset.counter);
                animateCounter(entry.target, 0, target, 2000);
                entry.target.dataset.animated = 'true';
            }
        });
    });

    counters.forEach(counter => observer.observe(counter));
}

// Initialize counter animations when DOM is loaded
document.addEventListener('DOMContentLoaded', setupCounterAnimations);

// Export functions for global use
window.WebsiteDeveloper = {
    showToast,
    formatCurrency,
    formatDateHindi,
    showButtonLoading,
    hideButtonLoading,
    debounce,
    throttle,
    isInViewport,
    animateCounter
};
