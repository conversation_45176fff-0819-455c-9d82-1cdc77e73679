<?php
require_once 'config/config.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('login.php');
}

$error = '';
$order_number = $_GET['order'] ?? '';

if (empty($order_number)) {
    redirect('client/dashboard.php');
}

// Get order details
$order = $db->fetchOne(
    "SELECT o.*, sp.name_hindi as package_name, u.full_name, u.email, u.phone 
     FROM orders o 
     JOIN service_packages sp ON o.package_id = sp.id 
     JOIN users u ON o.user_id = u.id 
     WHERE o.order_number = ? AND o.user_id = ?",
    [$order_number, get_current_user_id()]
);

if (!$order) {
    redirect('client/dashboard.php');
}

// Check if already paid
if ($order['payment_status'] === 'paid') {
    redirect('client/dashboard.php');
}

// Check if amount is 0 (custom development)
if ($order['total_amount'] <= 0) {
    redirect('client/dashboard.php');
}

// Generate Razorpay order (in real implementation, you would use Razorpay API)
$razorpay_order_id = 'order_' . uniqid();
$razorpay_key_id = RAZORPAY_KEY_ID ?: 'rzp_test_1234567890'; // Demo key

// Update order with Razorpay order ID
$db->update('orders', 
    ['razorpay_order_id' => $razorpay_order_id], 
    'id = ?', 
    [$order['id']]
);
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>भुगतान - <?php echo SITE_NAME_HINDI; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Razorpay Checkout -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="client/dashboard.php">डैशबोर्ड</a>
                <a class="nav-link" href="logout.php">लॉगआउट</a>
            </div>
        </div>
    </nav>

    <!-- Payment Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow-lg border-0">
                        <div class="card-header bg-primary text-white text-center py-4">
                            <h3 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>भुगतान करें
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <!-- Order Summary -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h5 class="mb-3">ऑर्डर विवरण</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>ऑर्डर नंबर:</strong></td>
                                            <td><?php echo $order['order_number']; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>पैकेज:</strong></td>
                                            <td><?php echo $order['package_name']; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>राशि:</strong></td>
                                            <td class="text-primary fw-bold"><?php echo format_currency($order['total_amount']); ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>स्थिति:</strong></td>
                                            <td><?php echo get_order_status_badge($order['status']); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5 class="mb-3">ग्राहक विवरण</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>नाम:</strong></td>
                                            <td><?php echo $order['full_name']; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>ईमेल:</strong></td>
                                            <td><?php echo $order['email']; ?></td>
                                        </tr>
                                        <tr>
                                            <td><strong>फोन:</strong></td>
                                            <td><?php echo $order['phone'] ?: 'N/A'; ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Payment Methods -->
                            <div class="mb-4">
                                <h5 class="mb-3">भुगतान विधि चुनें</h5>
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="payment-method active" data-method="razorpay">
                                            <i class="fas fa-credit-card fa-2x mb-2"></i>
                                            <h6>Razorpay</h6>
                                            <small class="text-muted">कार्ड, UPI, नेट बैंकिंग</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="payment-method" data-method="upi">
                                            <i class="fas fa-mobile-alt fa-2x mb-2"></i>
                                            <h6>UPI</h6>
                                            <small class="text-muted">PhonePe, GPay, Paytm</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="payment-method" data-method="netbanking">
                                            <i class="fas fa-university fa-2x mb-2"></i>
                                            <h6>नेट बैंकिंग</h6>
                                            <small class="text-muted">सभी प्रमुख बैंक</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Button -->
                            <div class="text-center">
                                <button type="button" class="btn btn-success btn-lg px-5" id="payButton">
                                    <i class="fas fa-lock me-2"></i>सुरक्षित भुगतान करें - <?php echo format_currency($order['total_amount']); ?>
                                </button>
                                <p class="text-muted mt-2 small">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    आपका भुगतान 256-bit SSL एन्क्रिप्शन द्वारा सुरक्षित है
                                </p>
                            </div>

                            <!-- Security Features -->
                            <div class="row mt-4">
                                <div class="col-md-4 text-center">
                                    <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                    <h6>सुरक्षित</h6>
                                    <small class="text-muted">SSL एन्क्रिप्शन</small>
                                </div>
                                <div class="col-md-4 text-center">
                                    <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                                    <h6>तुरंत</h6>
                                    <small class="text-muted">तत्काल प्रोसेसिंग</small>
                                </div>
                                <div class="col-md-4 text-center">
                                    <i class="fas fa-headset fa-2x text-warning mb-2"></i>
                                    <h6>सहायता</h6>
                                    <small class="text-muted">24/7 सपोर्ट</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Help Section -->
                    <div class="card mt-4 border-info">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-question-circle me-2"></i>भुगतान में समस्या?
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-2">यदि आपको भुगतान में कोई समस्या आ रही है, तो हमसे संपर्क करें:</p>
                            <div class="row">
                                <div class="col-md-4">
                                    <a href="https://wa.me/919876543210" class="btn btn-success btn-sm w-100" target="_blank">
                                        <i class="fab fa-whatsapp me-1"></i>WhatsApp
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="tel:+919876543210" class="btn btn-primary btn-sm w-100">
                                        <i class="fas fa-phone me-1"></i>कॉल करें
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="mailto:<EMAIL>" class="btn btn-secondary btn-sm w-100">
                                        <i class="fas fa-envelope me-1"></i>ईमेल
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Payment method selection
        document.querySelectorAll('.payment-method').forEach(function(method) {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(function(m) {
                    m.classList.remove('active');
                });
                this.classList.add('active');
            });
        });

        // Razorpay payment options
        var options = {
            "key": "<?php echo $razorpay_key_id; ?>",
            "amount": <?php echo $order['total_amount'] * 100; ?>, // Amount in paise
            "currency": "INR",
            "name": "<?php echo SITE_NAME; ?>",
            "description": "<?php echo $order['package_name']; ?>",
            "order_id": "<?php echo $razorpay_order_id; ?>",
            "handler": function (response) {
                // Payment successful
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = 'payment-success.php';
                
                var fields = {
                    'razorpay_payment_id': response.razorpay_payment_id,
                    'razorpay_order_id': response.razorpay_order_id,
                    'razorpay_signature': response.razorpay_signature,
                    'order_number': '<?php echo $order['order_number']; ?>'
                };
                
                for (var key in fields) {
                    var input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = key;
                    input.value = fields[key];
                    form.appendChild(input);
                }
                
                document.body.appendChild(form);
                form.submit();
            },
            "prefill": {
                "name": "<?php echo $order['full_name']; ?>",
                "email": "<?php echo $order['email']; ?>",
                "contact": "<?php echo $order['phone']; ?>"
            },
            "notes": {
                "order_number": "<?php echo $order['order_number']; ?>"
            },
            "theme": {
                "color": "#0d6efd"
            },
            "modal": {
                "ondismiss": function() {
                    console.log('Payment cancelled');
                }
            }
        };

        var rzp1 = new Razorpay(options);

        document.getElementById('payButton').onclick = function(e) {
            e.preventDefault();
            rzp1.open();
        }

        // Handle payment failure
        rzp1.on('payment.failed', function (response) {
            alert('भुगतान असफल: ' + response.error.description);
            console.log(response.error);
        });
    </script>
    
    <style>
        .payment-method {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .payment-method:hover {
            border-color: var(--bs-primary);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .payment-method.active {
            border-color: var(--bs-primary);
            background: rgba(13, 110, 253, 0.1);
        }
        
        .payment-method i {
            color: var(--bs-primary);
        }
        
        .payment-method h6 {
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
    </style>
</body>
</html>
