<?php
require_once 'config/config.php';

$error = '';
$success = '';

// Handle contact form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $subject = sanitize_input($_POST['subject'] ?? '');
    $message = sanitize_input($_POST['message'] ?? '');
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!verify_csrf_token($csrf_token)) {
        $error = 'सुरक्षा त्रुटि। कृपया पुनः प्रयास करें।';
    } elseif (empty($name) || empty($email) || empty($message)) {
        $error = 'कृपया सभी आवश्यक फ़ील्ड भरें।';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'कृपया वैध ईमेल पता दर्ज करें।';
    } else {
        try {
            // Insert contact inquiry
            $inquiry_id = $db->insert('contact_inquiries', [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'subject' => $subject,
                'message' => $message,
                'status' => 'new'
            ]);
            
            if ($inquiry_id) {
                // Send thank you email to customer
                $customer_subject = 'हमसे संपर्क करने के लिए धन्यवाद';
                $customer_message = "प्रिय {$name},\n\nहमसे संपर्क करने के लिए धन्यवाद। हमें आपकी पूछताछ मिल गई है और हम 24 घंटे के भीतर आपसे संपर्क करेंगे।\n\nआपका संदेश:\n{$message}\n\nसादर,\nवेबसाइट डेवलपर टीम";
                
                // Log email (in real implementation, send actual email)
                $db->insert('email_logs', [
                    'to_email' => $email,
                    'subject' => $customer_subject,
                    'status' => 'sent'
                ]);
                
                // Send notification email to admin
                $admin_subject = "नई संपर्क पूछताछ - {$subject}";
                $admin_message = "नई संपर्क पूछताछ प्राप्त हुई:\n\nनाम: {$name}\nईमेल: {$email}\nफोन: {$phone}\nविषय: {$subject}\n\nसंदेश:\n{$message}";
                
                $db->insert('email_logs', [
                    'to_email' => ADMIN_EMAIL,
                    'subject' => $admin_subject,
                    'status' => 'sent'
                ]);
                
                $success = 'आपका संदेश सफलतापूर्वक भेज दिया गया है। हम जल्द ही आपसे संपर्क करेंगे।';
                
                // Clear form data
                $name = $email = $phone = $subject = $message = '';
            } else {
                $error = 'संदेश भेजने में त्रुटि। कृपया बाद में प्रयास करें।';
            }
        } catch (Exception $e) {
            error_log("Contact form error: " . $e->getMessage());
            $error = 'संदेश भेजने में त्रुटि। कृपया बाद में प्रयास करें।';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>संपर्क करें - <?php echo SITE_NAME_HINDI; ?></title>
    <meta name="description" content="हमसे संपर्क करें। वेब डेवलपमेंट सेवाओं के लिए हमारी टीम से बात करें।">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- reCAPTCHA -->
    <?php if (RECAPTCHA_SITE_KEY): ?>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <?php endif; ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">होम</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">सेवाएं</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">हमारे बारे में</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="blog.php">ब्लॉग</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="contact.php">संपर्क</a>
                    </li>
                    <?php if (is_logged_in()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>डैशबोर्ड
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (is_admin()): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">एडमिन डैशबोर्ड</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="client/dashboard.php">क्लाइंट डैशबोर्ड</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">लॉगआउट</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">लॉगिन</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-outline-light ms-2 px-3" href="register.php">रजिस्टर करें</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-primary text-white py-5" style="margin-top: 76px;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-5 fw-bold mb-3">हमसे संपर्क करें</h1>
                    <p class="lead mb-0">आपके प्रोजेक्ट के बारे में बात करने के लिए हम उत्सुक हैं। आज ही संपर्क करें!</p>
                </div>
                <div class="col-lg-6 text-center">
                    <i class="fas fa-headset fa-5x text-warning"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Contact Form -->
                <div class="col-lg-8">
                    <div class="contact-form">
                        <h2 class="mb-4">संदेश भेजें</h2>
                        
                        <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <form method="POST" action="contact.php" id="contactForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-user me-1"></i>पूरा नाम *
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($name ?? ''); ?>" 
                                           required placeholder="आपका पूरा नाम">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>ईमेल पता *
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($email ?? ''); ?>" 
                                           required placeholder="आपका ईमेल पता">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="fas fa-phone me-1"></i>फोन नंबर
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($phone ?? ''); ?>" 
                                           placeholder="आपका फोन नंबर">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">
                                        <i class="fas fa-tag me-1"></i>विषय
                                    </label>
                                    <select class="form-control" id="subject" name="subject">
                                        <option value="">विषय चुनें</option>
                                        <option value="वेब डेवलपमेंट" <?php echo ($subject ?? '') === 'वेब डेवलपमेंट' ? 'selected' : ''; ?>>वेब डेवलपमेंट</option>
                                        <option value="ई-कॉमर्स" <?php echo ($subject ?? '') === 'ई-कॉमर्स' ? 'selected' : ''; ?>>ई-कॉमर्स</option>
                                        <option value="कस्टम डेवलपमेंट" <?php echo ($subject ?? '') === 'कस्टम डेवलपमेंट' ? 'selected' : ''; ?>>कस्टम डेवलपमेंट</option>
                                        <option value="सहायता" <?php echo ($subject ?? '') === 'सहायता' ? 'selected' : ''; ?>>सहायता</option>
                                        <option value="अन्य" <?php echo ($subject ?? '') === 'अन्य' ? 'selected' : ''; ?>>अन्य</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="message" class="form-label">
                                    <i class="fas fa-comment me-1"></i>संदेश *
                                </label>
                                <textarea class="form-control" id="message" name="message" rows="6" 
                                          required placeholder="आपका संदेश यहाँ लिखें..."><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                            </div>
                            
                            <?php if (RECAPTCHA_SITE_KEY): ?>
                            <div class="mb-3">
                                <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                            </div>
                            <?php endif; ?>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>संदेश भेजें
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="col-lg-4">
                    <!-- Contact Details -->
                    <div class="card shadow border-0 mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>संपर्क जानकारी
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="contact-item mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-envelope fa-lg text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">ईमेल</h6>
                                        <a href="mailto:<EMAIL>" class="text-muted">
                                            <EMAIL>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-phone fa-lg text-success"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">फोन</h6>
                                        <a href="tel:+919876543210" class="text-muted">
                                            +91-9876543210
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="contact-icon me-3">
                                        <i class="fab fa-whatsapp fa-lg text-success"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">WhatsApp</h6>
                                        <a href="https://wa.me/919876543210" class="text-muted" target="_blank">
                                            +91-9876543210
                                        </a>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="d-flex align-items-center">
                                    <div class="contact-icon me-3">
                                        <i class="fas fa-clock fa-lg text-warning"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">कार्य समय</h6>
                                        <p class="text-muted mb-0">सोमवार - शनिवार<br>9:00 AM - 6:00 PM</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Contact -->
                    <div class="card shadow border-0 mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>त्वरित संपर्क
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="https://wa.me/919876543210" class="btn btn-success" target="_blank">
                                    <i class="fab fa-whatsapp me-2"></i>WhatsApp चैट
                                </a>
                                <a href="tel:+919876543210" class="btn btn-primary">
                                    <i class="fas fa-phone me-2"></i>तुरंत कॉल करें
                                </a>
                                <a href="mailto:<EMAIL>" class="btn btn-secondary">
                                    <i class="fas fa-envelope me-2"></i>ईमेल भेजें
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-question-circle me-2"></i>अक्सर पूछे जाने वाले प्रश्न
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="accordion" id="faqAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                            वेबसाइट बनने में कितना समय लगता है?
                                        </button>
                                    </h2>
                                    <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            सामान्यतः 15-30 दिन, प्रोजेक्ट की जटिलता के आधार पर।
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                            क्या आप होस्टिंग भी प्रदान करते हैं?
                                        </button>
                                    </h2>
                                    <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            हाँ, हम मुफ्त डोमेन और होस्टिंग सेटअप प्रदान करते हैं।
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3"><?php echo SITE_NAME_HINDI; ?></h5>
                    <p class="text-muted">भारतीय बाजार के लिए पेशेवर वेब डेवलपमेंट सेवाएं।</p>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">सेवाएं</h6>
                    <ul class="list-unstyled">
                        <li><a href="services.php" class="text-muted text-decoration-none">वेब डेवलपमेंट</a></li>
                        <li><a href="services.php" class="text-muted text-decoration-none">ई-कॉमर्स</a></li>
                    </ul>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">कंपनी</h6>
                    <ul class="list-unstyled">
                        <li><a href="about.php" class="text-muted text-decoration-none">हमारे बारे में</a></li>
                        <li><a href="blog.php" class="text-muted text-decoration-none">ब्लॉग</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h6 class="fw-bold mb-3">संपर्क</h6>
                    <p class="text-muted mb-0"><EMAIL><br>+91-9876543210</p>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0 text-muted">&copy; 2024 <?php echo SITE_NAME_HINDI; ?>. सभी अधिकार सुरक्षित।</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const message = document.getElementById('message').value.trim();
            
            if (!name || !email || !message) {
                e.preventDefault();
                alert('कृपया सभी आवश्यक फ़ील्ड भरें।');
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="loading me-2"></span>भेजा जा रहा है...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
