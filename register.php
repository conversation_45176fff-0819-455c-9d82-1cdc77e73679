<?php
require_once 'config/config.php';

$error = '';
$success = '';

// Check if user is already logged in
if (is_logged_in()) {
    if (is_admin()) {
        redirect('admin/dashboard.php');
    } else {
        redirect('client/dashboard.php');
    }
}

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $full_name = sanitize_input($_POST['full_name'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!verify_csrf_token($csrf_token)) {
        $error = 'सुरक्षा त्रुटि। कृपया पुनः प्रयास करें।';
    } elseif (empty($username) || empty($email) || empty($full_name) || empty($password)) {
        $error = 'कृपया सभी आवश्यक फ़ील्ड भरें।';
    } elseif ($password !== $confirm_password) {
        $error = 'पासवर्ड मेल नहीं खाते।';
    } elseif (strlen($password) < 6) {
        $error = 'पासवर्ड कम से कम 6 अक्षर का होना चाहिए।';
    } else {
        try {
            // Check if username or email already exists
            $existing_user = $db->fetchOne(
                "SELECT id FROM users WHERE username = ? OR email = ?",
                [$username, $email]
            );
            
            if ($existing_user) {
                $error = 'यूजरनेम या ईमेल पहले से मौजूद है।';
            } else {
                // Create new user
                $password_hash = password_hash($password, PASSWORD_DEFAULT);
                $verification_token = bin2hex(random_bytes(32));
                
                $user_id = $db->insert('users', [
                    'username' => $username,
                    'email' => $email,
                    'password_hash' => $password_hash,
                    'full_name' => $full_name,
                    'phone' => $phone,
                    'role' => 'client',
                    'status' => 'active',
                    'verification_token' => $verification_token,
                    'email_verified' => 1 // Auto-verify for demo
                ]);
                
                if ($user_id) {
                    // Log activity
                    log_activity($user_id, 'register', 'User registered');
                    
                    // Send welcome email (implement later)
                    // send_email($email, 'Welcome to Website Developer', $welcome_message);
                    
                    $success = 'रजिस्ट्रेशन सफल! अब आप लॉगिन कर सकते हैं।';
                    
                    // Auto login the user
                    $_SESSION['user_id'] = $user_id;
                    $_SESSION['user_email'] = $email;
                    $_SESSION['user_name'] = $full_name;
                    $_SESSION['user_role'] = 'client';
                    $_SESSION['last_activity'] = time();
                    
                    // Redirect to client dashboard
                    redirect('client/dashboard.php');
                } else {
                    $error = 'रजिस्ट्रेशन में त्रुटि। कृपया बाद में प्रयास करें।';
                }
            }
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            $error = 'रजिस्ट्रेशन में त्रुटि। कृपया बाद में प्रयास करें।';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>रजिस्टर करें - <?php echo SITE_NAME_HINDI; ?></title>
    <meta name="description" content="नया अकाउंट बनाएं">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- reCAPTCHA -->
    <?php if (RECAPTCHA_SITE_KEY): ?>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <?php endif; ?>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">होम</a>
                <a class="nav-link" href="login.php">लॉगिन</a>
            </div>
        </div>
    </nav>

    <!-- Registration Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card shadow-lg border-0">
                        <div class="card-header bg-primary text-white text-center py-4">
                            <h3 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>नया अकाउंट बनाएं
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($success): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <?php endif; ?>

                            <form method="POST" action="register.php" id="registerForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="full_name" class="form-label">
                                            <i class="fas fa-user me-1"></i>पूरा नाम *
                                        </label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?php echo htmlspecialchars($full_name ?? ''); ?>" 
                                               required placeholder="आपका पूरा नाम">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">
                                            <i class="fas fa-at me-1"></i>यूजरनेम *
                                        </label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="<?php echo htmlspecialchars($username ?? ''); ?>" 
                                               required placeholder="यूजरनेम चुनें">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            <i class="fas fa-envelope me-1"></i>ईमेल पता *
                                        </label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($email ?? ''); ?>" 
                                               required placeholder="आपका ईमेल पता">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">
                                            <i class="fas fa-phone me-1"></i>फोन नंबर
                                        </label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo htmlspecialchars($phone ?? ''); ?>" 
                                               placeholder="आपका फोन नंबर">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="password" class="form-label">
                                            <i class="fas fa-lock me-1"></i>पासवर्ड *
                                        </label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="password" name="password" 
                                                   required placeholder="पासवर्ड (कम से कम 6 अक्षर)">
                                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">
                                            <i class="fas fa-lock me-1"></i>पासवर्ड पुष्टि *
                                        </label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   required placeholder="पासवर्ड दोबारा दर्ज करें">
                                            <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        मैं <a href="terms.php" target="_blank">नियम और शर्तों</a> से सहमत हूं *
                                    </label>
                                </div>
                                
                                <?php if (RECAPTCHA_SITE_KEY): ?>
                                <div class="mb-3">
                                    <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-user-plus me-2"></i>अकाउंट बनाएं
                                    </button>
                                </div>
                            </form>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-0">
                                    पहले से अकाउंट है? 
                                    <a href="login.php" class="text-decoration-none fw-bold">
                                        यहाँ लॉगिन करें
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 <?php echo SITE_NAME_HINDI; ?>. सभी अधिकार सुरक्षित।</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="privacy.php" class="text-muted text-decoration-none me-3">प्राइवेसी पॉलिसी</a>
                    <a href="terms.php" class="text-muted text-decoration-none">नियम और शर्तें</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toggle password visibility
        function togglePasswordField(toggleId, passwordId) {
            document.getElementById(toggleId).addEventListener('click', function() {
                const password = document.getElementById(passwordId);
                const icon = this.querySelector('i');
                
                if (password.type === 'password') {
                    password.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    password.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }

        togglePasswordField('togglePassword', 'password');
        togglePasswordField('toggleConfirmPassword', 'confirm_password');

        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const terms = document.getElementById('terms').checked;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('पासवर्ड मेल नहीं खाते।');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('पासवर्ड कम से कम 6 अक्षर का होना चाहिए।');
                return false;
            }
            
            if (!terms) {
                e.preventDefault();
                alert('कृपया नियम और शर्तों से सहमत हों।');
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="loading me-2"></span>अकाउंट बनाया जा रहा है...';
            submitBtn.disabled = true;
        });

        // Username validation
        document.getElementById('username').addEventListener('input', function() {
            this.value = this.value.toLowerCase().replace(/[^a-z0-9_]/g, '');
        });
    </script>
</body>
</html>
