/**
 * Enhanced JavaScript for Website Developer Services
 * Advanced features and animations
 */

// Enhanced Global Configuration
const WebsiteEnhanced = {
    config: {
        animationDuration: 600,
        scrollOffset: 100,
        debounceDelay: 300,
        lazyLoadOffset: 200,
        counterSpeed: 2000,
        typewriterSpeed: 100
    },
    
    state: {
        isScrolling: false,
        activeAnimations: new Set(),
        loadedImages: new Set(),
        countersAnimated: new Set()
    },
    
    observers: {
        intersection: null,
        mutation: null,
        resize: null
    }
};

// Enhanced Document Ready
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedFeatures();
    setupAdvancedAnimations();
    setupIntersectionObserver();
    setupPerformanceOptimizations();
    setupAccessibilityFeatures();
});

// Initialize Enhanced Features
function initializeEnhancedFeatures() {
    // Enhanced tooltips and popovers
    initializeEnhancedTooltips();
    
    // Advanced scroll effects
    initializeScrollEffects();
    
    // Counter animations
    initializeCounterAnimations();
    
    // Typewriter effects
    initializeTypewriterEffects();
    
    // Particle effects
    initializeParticleEffects();
    
    // Advanced form validations
    initializeAdvancedForms();
    
    // Image lazy loading with blur effect
    initializeLazyLoading();
    
    // Smooth page transitions
    initializePageTransitions();
}

// Enhanced Tooltips
function initializeEnhancedTooltips() {
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip, {
            animation: true,
            delay: { show: 300, hide: 100 },
            html: true,
            customClass: 'enhanced-tooltip'
        });
    });
    
    const popovers = document.querySelectorAll('[data-bs-toggle="popover"]');
    popovers.forEach(popover => {
        new bootstrap.Popover(popover, {
            animation: true,
            html: true,
            trigger: 'hover focus',
            customClass: 'enhanced-popover'
        });
    });
}

// Advanced Scroll Effects
function initializeScrollEffects() {
    let ticking = false;
    
    function updateScrollEffects() {
        const scrollY = window.pageYOffset;
        const windowHeight = window.innerHeight;
        
        // Parallax effects
        const parallaxElements = document.querySelectorAll('.parallax');
        parallaxElements.forEach(element => {
            const speed = element.dataset.speed || 0.5;
            const yPos = -(scrollY * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
        
        // Navbar background opacity
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            const opacity = Math.min(scrollY / 100, 1);
            navbar.style.backgroundColor = `rgba(13, 110, 253, ${opacity})`;
        }
        
        // Scroll progress indicator
        updateScrollProgress();
        
        ticking = false;
    }
    
    function requestScrollUpdate() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    }
    
    window.addEventListener('scroll', requestScrollUpdate, { passive: true });
}

// Scroll Progress Indicator
function updateScrollProgress() {
    const progressBar = document.querySelector('.scroll-progress');
    if (!progressBar) return;
    
    const scrollTop = document.documentElement.scrollTop;
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
    const progress = (scrollTop / scrollHeight) * 100;
    
    progressBar.style.width = `${progress}%`;
}

// Counter Animations
function initializeCounterAnimations() {
    const counters = document.querySelectorAll('[data-counter]');
    
    counters.forEach(counter => {
        const target = parseInt(counter.dataset.counter);
        const duration = parseInt(counter.dataset.duration) || WebsiteEnhanced.config.counterSpeed;
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !WebsiteEnhanced.state.countersAnimated.has(counter)) {
                    animateCounter(counter, target, duration);
                    WebsiteEnhanced.state.countersAnimated.add(counter);
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(counter);
    });
}

function animateCounter(element, target, duration) {
    let start = 0;
    const increment = target / (duration / 16);
    
    function updateCounter() {
        start += increment;
        if (start < target) {
            element.textContent = Math.floor(start).toLocaleString('hi-IN');
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = target.toLocaleString('hi-IN');
        }
    }
    
    updateCounter();
}

// Typewriter Effect
function initializeTypewriterEffects() {
    const typewriters = document.querySelectorAll('.typewriter');
    
    typewriters.forEach(typewriter => {
        const text = typewriter.textContent;
        const speed = parseInt(typewriter.dataset.speed) || WebsiteEnhanced.config.typewriterSpeed;
        
        typewriter.textContent = '';
        typewriter.style.borderRight = '2px solid';
        typewriter.style.animation = 'blink 1s infinite';
        
        let i = 0;
        function typeChar() {
            if (i < text.length) {
                typewriter.textContent += text.charAt(i);
                i++;
                setTimeout(typeChar, speed);
            } else {
                typewriter.style.borderRight = 'none';
                typewriter.style.animation = 'none';
            }
        }
        
        // Start typing when element comes into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(typeChar, 500);
                    observer.unobserve(typewriter);
                }
            });
        });
        
        observer.observe(typewriter);
    });
}

// Particle Effects
function initializeParticleEffects() {
    const particleContainers = document.querySelectorAll('.particles');
    
    particleContainers.forEach(container => {
        createParticles(container);
    });
}

function createParticles(container) {
    const particleCount = parseInt(container.dataset.particles) || 50;
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            pointer-events: none;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: float ${3 + Math.random() * 4}s infinite linear;
        `;
        container.appendChild(particle);
    }
}

// Advanced Form Validations
function initializeAdvancedForms() {
    const forms = document.querySelectorAll('.enhanced-form');
    
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            // Real-time validation
            input.addEventListener('input', debounce(() => {
                validateField(input);
            }, 300));
            
            // Enhanced focus effects
            input.addEventListener('focus', () => {
                input.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', () => {
                input.parentElement.classList.remove('focused');
                validateField(input);
            });
        });
        
        // Enhanced form submission
        form.addEventListener('submit', handleEnhancedSubmit);
    });
}

function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const required = field.hasAttribute('required');
    
    let isValid = true;
    let message = '';
    
    // Required validation
    if (required && !value) {
        isValid = false;
        message = 'यह फील्ड आवश्यक है';
    }
    
    // Email validation
    if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            message = 'कृपया वैध ईमेल पता दर्ज करें';
        }
    }
    
    // Phone validation
    if (field.name === 'phone' && value) {
        const phoneRegex = /^[6-9]\d{9}$/;
        if (!phoneRegex.test(value)) {
            isValid = false;
            message = 'कृपया वैध फोन नंबर दर्ज करें';
        }
    }
    
    // Update field state
    updateFieldState(field, isValid, message);
    
    return isValid;
}

function updateFieldState(field, isValid, message) {
    const container = field.parentElement;
    const feedback = container.querySelector('.invalid-feedback') || 
                    container.appendChild(document.createElement('div'));
    
    feedback.className = 'invalid-feedback';
    feedback.textContent = message;
    
    field.classList.toggle('is-valid', isValid && field.value.trim());
    field.classList.toggle('is-invalid', !isValid);
}

// Enhanced Form Submission
function handleEnhancedSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const submitBtn = form.querySelector('[type="submit"]');
    const originalText = submitBtn.textContent;
    
    // Validate all fields
    const inputs = form.querySelectorAll('input, textarea, select');
    let isFormValid = true;
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isFormValid = false;
        }
    });
    
    if (!isFormValid) {
        showNotification('कृपया सभी फील्ड सही तरीके से भरें', 'error');
        return;
    }
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="loading me-2"></span>भेजा जा रहा है...';
    
    // Submit form data
    const formData = new FormData(form);
    
    fetch(form.action || window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'सफलतापूर्वक भेजा गया', 'success');
            form.reset();
        } else {
            showNotification(data.message || 'कुछ गलत हुआ', 'error');
        }
    })
    .catch(error => {
        showNotification('नेटवर्क त्रुटि', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}

// Enhanced Lazy Loading
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                loadImageWithBlur(img);
                imageObserver.unobserve(img);
            }
        });
    }, {
        rootMargin: `${WebsiteEnhanced.config.lazyLoadOffset}px`
    });
    
    images.forEach(img => imageObserver.observe(img));
}

function loadImageWithBlur(img) {
    // Create a low-quality placeholder
    const placeholder = img.dataset.placeholder;
    if (placeholder) {
        img.src = placeholder;
        img.style.filter = 'blur(5px)';
    }
    
    // Load the actual image
    const actualImg = new Image();
    actualImg.onload = () => {
        img.src = actualImg.src;
        img.style.filter = 'none';
        img.style.transition = 'filter 0.3s ease';
        WebsiteEnhanced.state.loadedImages.add(img);
    };
    actualImg.src = img.dataset.src;
}

// Notification System
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)} me-2"></i>
            <span>${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    // Add to container
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }
    
    container.appendChild(notification);
    
    // Auto remove
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, duration);
    
    // Animate in
    requestAnimationFrame(() => {
        notification.classList.add('show');
    });
}

function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || icons.info;
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Setup Intersection Observer for animations
function setupIntersectionObserver() {
    WebsiteEnhanced.observers.intersection = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animationClass = element.dataset.animation || 'fade-in-admin';
                element.classList.add(animationClass);
                WebsiteEnhanced.state.activeAnimations.add(element);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });
    
    // Observe all elements with data-animation
    document.querySelectorAll('[data-animation]').forEach(el => {
        WebsiteEnhanced.observers.intersection.observe(el);
    });
}

// CSS Animations
const style = document.createElement('style');
style.textContent = `
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-10px) rotate(120deg); }
        66% { transform: translateY(5px) rotate(240deg); }
    }
    
    .notification-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
    }
    
    .notification {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        margin-bottom: 10px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .notification.show {
        transform: translateX(0);
    }
    
    .notification-success { border-left: 4px solid #198754; }
    .notification-error { border-left: 4px solid #dc3545; }
    .notification-warning { border-left: 4px solid #ffc107; }
    .notification-info { border-left: 4px solid #0dcaf0; }
    
    .notification-content {
        padding: 15px;
        display: flex;
        align-items: center;
    }
    
    .notification-close {
        background: none;
        border: none;
        margin-left: auto;
        cursor: pointer;
        opacity: 0.7;
    }
    
    .notification-close:hover {
        opacity: 1;
    }
    
    .scroll-progress {
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: linear-gradient(90deg, #0d6efd, #ffc107);
        z-index: 9999;
        transition: width 0.1s ease;
    }
    
    .enhanced-tooltip {
        --bs-tooltip-bg: #212529;
        --bs-tooltip-opacity: 0.95;
    }
    
    .enhanced-popover {
        --bs-popover-max-width: 300px;
    }
`;
document.head.appendChild(style);

// Add scroll progress bar
document.addEventListener('DOMContentLoaded', () => {
    const progressBar = document.createElement('div');
    progressBar.className = 'scroll-progress';
    document.body.appendChild(progressBar);
});

console.log('Enhanced JavaScript loaded successfully! 🚀');
