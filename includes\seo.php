<?php
/**
 * SEO and Analytics Helper Functions
 * Handles meta tags, structured data, and Google Analytics
 */

class SEO {
    private $db;
    private $page_title;
    private $page_description;
    private $page_keywords;
    private $page_image;
    private $page_url;
    private $structured_data;
    
    public function __construct($database) {
        $this->db = $database;
        $this->page_url = $this->getCurrentURL();
        $this->structured_data = [];
    }
    
    /**
     * Set page title
     */
    public function setTitle($title) {
        $this->page_title = $title;
        return $this;
    }
    
    /**
     * Set page description
     */
    public function setDescription($description) {
        $this->page_description = $description;
        return $this;
    }
    
    /**
     * Set page keywords
     */
    public function setKeywords($keywords) {
        if (is_array($keywords)) {
            $this->page_keywords = implode(', ', $keywords);
        } else {
            $this->page_keywords = $keywords;
        }
        return $this;
    }
    
    /**
     * Set page image for social sharing
     */
    public function setImage($image_url) {
        $this->page_image = $image_url;
        return $this;
    }
    
    /**
     * Generate meta tags
     */
    public function generateMetaTags() {
        $html = '';
        
        // Basic meta tags
        if ($this->page_title) {
            $html .= '<title>' . htmlspecialchars($this->page_title) . '</title>' . "\n";
        }
        
        if ($this->page_description) {
            $html .= '<meta name="description" content="' . htmlspecialchars($this->page_description) . '">' . "\n";
        }
        
        if ($this->page_keywords) {
            $html .= '<meta name="keywords" content="' . htmlspecialchars($this->page_keywords) . '">' . "\n";
        }
        
        // Open Graph tags for Facebook
        $html .= '<meta property="og:type" content="website">' . "\n";
        $html .= '<meta property="og:url" content="' . htmlspecialchars($this->page_url) . '">' . "\n";
        
        if ($this->page_title) {
            $html .= '<meta property="og:title" content="' . htmlspecialchars($this->page_title) . '">' . "\n";
        }
        
        if ($this->page_description) {
            $html .= '<meta property="og:description" content="' . htmlspecialchars($this->page_description) . '">' . "\n";
        }
        
        if ($this->page_image) {
            $html .= '<meta property="og:image" content="' . htmlspecialchars($this->page_image) . '">' . "\n";
        }
        
        // Twitter Card tags
        $html .= '<meta name="twitter:card" content="summary_large_image">' . "\n";
        $html .= '<meta name="twitter:url" content="' . htmlspecialchars($this->page_url) . '">' . "\n";
        
        if ($this->page_title) {
            $html .= '<meta name="twitter:title" content="' . htmlspecialchars($this->page_title) . '">' . "\n";
        }
        
        if ($this->page_description) {
            $html .= '<meta name="twitter:description" content="' . htmlspecialchars($this->page_description) . '">' . "\n";
        }
        
        if ($this->page_image) {
            $html .= '<meta name="twitter:image" content="' . htmlspecialchars($this->page_image) . '">' . "\n";
        }
        
        // Additional SEO meta tags
        $html .= '<meta name="robots" content="index, follow">' . "\n";
        $html .= '<meta name="author" content="' . SITE_NAME . '">' . "\n";
        $html .= '<meta name="language" content="hi">' . "\n";
        $html .= '<link rel="canonical" href="' . htmlspecialchars($this->page_url) . '">' . "\n";
        
        return $html;
    }
    
    /**
     * Add structured data
     */
    public function addStructuredData($type, $data) {
        $this->structured_data[] = [
            '@context' => 'https://schema.org',
            '@type' => $type,
            ...$data
        ];
        return $this;
    }
    
    /**
     * Generate structured data JSON-LD
     */
    public function generateStructuredData() {
        if (empty($this->structured_data)) {
            return '';
        }
        
        $html = '<script type="application/ld+json">' . "\n";
        $html .= json_encode($this->structured_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        $html .= "\n" . '</script>' . "\n";
        
        return $html;
    }
    
    /**
     * Generate organization structured data
     */
    public function addOrganizationData() {
        $this->addStructuredData('Organization', [
            'name' => SITE_NAME,
            'alternateName' => SITE_NAME_HINDI,
            'url' => SITE_URL,
            'logo' => SITE_URL . '/assets/images/logo.png',
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => '+91-9876543210',
                'contactType' => 'customer service',
                'areaServed' => 'IN',
                'availableLanguage' => ['Hindi', 'English']
            ],
            'address' => [
                '@type' => 'PostalAddress',
                'addressCountry' => 'IN'
            ],
            'sameAs' => [
                'https://www.facebook.com/websitedeveloper0002',
                'https://www.twitter.com/websitedeveloper0002',
                'https://www.linkedin.com/company/websitedeveloper0002'
            ]
        ]);
        return $this;
    }
    
    /**
     * Generate service structured data
     */
    public function addServiceData($service_name, $description, $price = null) {
        $service_data = [
            'name' => $service_name,
            'description' => $description,
            'provider' => [
                '@type' => 'Organization',
                'name' => SITE_NAME
            ],
            'areaServed' => 'IN',
            'availableLanguage' => ['Hindi', 'English']
        ];
        
        if ($price) {
            $service_data['offers'] = [
                '@type' => 'Offer',
                'price' => $price,
                'priceCurrency' => 'INR'
            ];
        }
        
        $this->addStructuredData('Service', $service_data);
        return $this;
    }
    
    /**
     * Generate breadcrumb structured data
     */
    public function addBreadcrumbData($breadcrumbs) {
        $items = [];
        $position = 1;
        
        foreach ($breadcrumbs as $name => $url) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => $name,
                'item' => $url
            ];
        }
        
        $this->addStructuredData('BreadcrumbList', [
            'itemListElement' => $items
        ]);
        return $this;
    }
    
    /**
     * Generate Google Analytics code
     */
    public function generateAnalyticsCode() {
        $ga_id = GA_TRACKING_ID;
        
        if (empty($ga_id)) {
            return '';
        }
        
        $html = "<!-- Google Analytics -->\n";
        $html .= "<script async src=\"https://www.googletagmanager.com/gtag/js?id={$ga_id}\"></script>\n";
        $html .= "<script>\n";
        $html .= "  window.dataLayer = window.dataLayer || [];\n";
        $html .= "  function gtag(){dataLayer.push(arguments);}\n";
        $html .= "  gtag('js', new Date());\n";
        $html .= "  gtag('config', '{$ga_id}');\n";
        $html .= "</script>\n";
        $html .= "<!-- End Google Analytics -->\n";
        
        return $html;
    }
    
    /**
     * Track custom event
     */
    public function trackEvent($action, $category = 'engagement', $label = '', $value = null) {
        $script = "<script>\n";
        $script .= "if (typeof gtag !== 'undefined') {\n";
        $script .= "  gtag('event', '{$action}', {\n";
        $script .= "    'event_category': '{$category}'";
        
        if ($label) {
            $script .= ",\n    'event_label': '{$label}'";
        }
        
        if ($value !== null) {
            $script .= ",\n    'value': {$value}";
        }
        
        $script .= "\n  });\n";
        $script .= "}\n";
        $script .= "</script>\n";
        
        return $script;
    }
    
    /**
     * Generate sitemap XML
     */
    public function generateSitemap() {
        $urls = [
            ['loc' => SITE_URL . '/', 'priority' => '1.0', 'changefreq' => 'daily'],
            ['loc' => SITE_URL . '/services.php', 'priority' => '0.9', 'changefreq' => 'weekly'],
            ['loc' => SITE_URL . '/about.php', 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['loc' => SITE_URL . '/contact.php', 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['loc' => SITE_URL . '/blog.php', 'priority' => '0.9', 'changefreq' => 'daily'],
        ];
        
        // Add blog posts
        $posts = $this->db->fetchAll(
            "SELECT slug, updated_at FROM blog_posts WHERE status = 'published' ORDER BY updated_at DESC"
        );
        
        foreach ($posts as $post) {
            $urls[] = [
                'loc' => SITE_URL . '/blog-post.php?slug=' . $post['slug'],
                'priority' => '0.7',
                'changefreq' => 'weekly',
                'lastmod' => date('Y-m-d', strtotime($post['updated_at']))
            ];
        }
        
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        foreach ($urls as $url) {
            $xml .= '  <url>' . "\n";
            $xml .= '    <loc>' . htmlspecialchars($url['loc']) . '</loc>' . "\n";
            $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
            $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
            
            if (isset($url['lastmod'])) {
                $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
            }
            
            $xml .= '  </url>' . "\n";
        }
        
        $xml .= '</urlset>';
        
        return $xml;
    }
    
    /**
     * Get current URL
     */
    private function getCurrentURL() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '/';
        
        return $protocol . '://' . $host . $uri;
    }
    
    /**
     * Generate robots.txt content
     */
    public function generateRobotsTxt() {
        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Disallow: /admin/\n";
        $content .= "Disallow: /config/\n";
        $content .= "Disallow: /database/\n";
        $content .= "Disallow: /classes/\n";
        $content .= "Disallow: /includes/\n";
        $content .= "\n";
        $content .= "Sitemap: " . SITE_URL . "/sitemap.xml\n";
        
        return $content;
    }
    
    /**
     * Optimize images for SEO
     */
    public function optimizeImageTags($src, $alt, $title = '', $width = null, $height = null) {
        $html = '<img src="' . htmlspecialchars($src) . '"';
        $html .= ' alt="' . htmlspecialchars($alt) . '"';
        
        if ($title) {
            $html .= ' title="' . htmlspecialchars($title) . '"';
        }
        
        if ($width) {
            $html .= ' width="' . (int)$width . '"';
        }
        
        if ($height) {
            $html .= ' height="' . (int)$height . '"';
        }
        
        $html .= ' loading="lazy"';
        $html .= '>';
        
        return $html;
    }
    
    /**
     * Generate meta tags for blog post
     */
    public function generateBlogPostMeta($post) {
        $this->setTitle($post['title_hindi'] . ' - ' . SITE_NAME_HINDI)
             ->setDescription($post['excerpt_hindi'] ?: substr(strip_tags($post['content_hindi']), 0, 160))
             ->setKeywords($post['tags'] ? json_decode($post['tags'], true) : []);
        
        if ($post['featured_image']) {
            $this->setImage(SITE_URL . '/' . $post['featured_image']);
        }
        
        // Add article structured data
        $this->addStructuredData('Article', [
            'headline' => $post['title_hindi'],
            'description' => $post['excerpt_hindi'],
            'author' => [
                '@type' => 'Person',
                'name' => $post['author_name'] ?? 'Website Developer'
            ],
            'publisher' => [
                '@type' => 'Organization',
                'name' => SITE_NAME,
                'logo' => [
                    '@type' => 'ImageObject',
                    'url' => SITE_URL . '/assets/images/logo.png'
                ]
            ],
            'datePublished' => $post['published_at'],
            'dateModified' => $post['updated_at'],
            'mainEntityOfPage' => [
                '@type' => 'WebPage',
                '@id' => SITE_URL . '/blog-post.php?slug=' . $post['slug']
            ]
        ]);
        
        return $this;
    }
}

// Helper function to create SEO instance
function createSEO($db) {
    return new SEO($db);
}
?>
