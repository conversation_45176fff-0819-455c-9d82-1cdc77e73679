<?php
require_once '../config/config.php';

// Check if user is logged in and is client
if (!is_logged_in()) {
    redirect('../login.php');
}

if (is_admin()) {
    redirect('../admin/dashboard.php');
}

$user_id = get_current_user_id();

// Get user statistics
$stats = $db->fetchOne(
    "SELECT 
        (SELECT COUNT(*) FROM orders WHERE user_id = ?) as total_orders,
        (SELECT COUNT(*) FROM orders WHERE user_id = ? AND status = 'pending') as pending_orders,
        (SELECT COUNT(*) FROM orders WHERE user_id = ? AND status = 'in_progress') as in_progress_orders,
        (SELECT COUNT(*) FROM orders WHERE user_id = ? AND status = 'completed') as completed_orders,
        (SELECT SUM(total_amount) FROM orders WHERE user_id = ? AND payment_status = 'paid') as total_paid,
        (SELECT COUNT(*) FROM orders WHERE user_id = ? AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as recent_orders",
    [$user_id, $user_id, $user_id, $user_id, $user_id, $user_id]
);

// Get recent orders
$recent_orders = $db->fetchAll(
    "SELECT o.*, sp.name_hindi as package_name 
     FROM orders o 
     JOIN service_packages sp ON o.package_id = sp.id 
     WHERE o.user_id = ? 
     ORDER BY o.created_at DESC 
     LIMIT 5",
    [$user_id]
);

// Get user profile
$user_profile = $db->fetchOne("SELECT * FROM users WHERE id = ?", [$user_id]);

// Success message handling
$success_message = $_SESSION['success_message'] ?? null;
unset($_SESSION['success_message']);
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>क्लाइंट डैशबोर्ड - <?php echo SITE_NAME_HINDI; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="../index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>डैशबोर्ड
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-1"></i>मेरे ऑर्डर
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.php">
                            <i class="fas fa-project-diagram me-1"></i>प्रोजेक्ट्स
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-1"></i>प्रोफाइल
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-home me-1"></i>होम पेज
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php">प्रोफाइल</a></li>
                            <li><a class="dropdown-item" href="settings.php">सेटिंग्स</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">लॉगआउट</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Success Message -->
        <?php if ($success_message): ?>
        <div class="alert alert-success admin-alert admin-alert-success fade-in-admin">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <!-- Welcome Section -->
        <div class="admin-page-header fade-in-admin">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="admin-page-title">नमस्कार, <?php echo $_SESSION['user_name']; ?>!</h1>
                    <p class="admin-page-subtitle">आपके डैशबोर्ड में आपका स्वागत है। यहाँ आप अपने सभी ऑर्डर और प्रोजेक्ट की स्थिति देख सकते हैं।</p>
                </div>
                <div>
                    <a href="../order.php" class="admin-btn admin-btn-primary">
                        <i class="fas fa-plus me-2"></i>नया ऑर्डर
                    </a>
                </div>
            </div>
        </div>

        <!-- Enhanced Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stats-number" data-counter="<?php echo $stats['total_orders'] ?? 0; ?>"><?php echo $stats['total_orders'] ?? 0; ?></div>
                    <div class="stats-label">कुल ऑर्डर</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card warning fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stats-number" data-counter="<?php echo $stats['in_progress_orders'] ?? 0; ?>"><?php echo $stats['in_progress_orders'] ?? 0; ?></div>
                    <div class="stats-label">प्रगति में</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card success fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number" data-counter="<?php echo $stats['completed_orders'] ?? 0; ?>"><?php echo $stats['completed_orders'] ?? 0; ?></div>
                    <div class="stats-label">पूर्ण ऑर्डर</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card info fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stats-number">₹<?php echo number_format($stats['total_paid'] ?? 0); ?></div>
                    <div class="stats-label">कुल भुगतान</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="admin-card slide-in-admin">
                    <div class="card-header">
                        <h5 class="mb-0">त्वरित कार्य</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="../order.php" class="quick-action-btn">
                                    <span class="quick-action-icon">
                                        <i class="fas fa-plus"></i>
                                    </span>
                                    नया ऑर्डर
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="orders.php" class="quick-action-btn">
                                    <span class="quick-action-icon">
                                        <i class="fas fa-list"></i>
                                    </span>
                                    ऑर्डर देखें
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="../contact.php" class="quick-action-btn">
                                    <span class="quick-action-icon">
                                        <i class="fas fa-headset"></i>
                                    </span>
                                    सहायता
                                </a>
                            </div>
                            <div class="col-lg-3 col-md-6 mb-3">
                                <a href="profile.php" class="quick-action-btn">
                                    <span class="quick-action-icon">
                                        <i class="fas fa-user-edit"></i>
                                    </span>
                                    प्रोफाइल अपडेट
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders and Profile -->
        <div class="row">
            <!-- Recent Orders -->
            <div class="col-lg-8 mb-4">
                <div class="admin-card slide-in-admin">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">हाल के ऑर्डर</h5>
                        <a href="orders.php" class="btn btn-sm btn-outline-primary">सभी देखें</a>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($recent_orders)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">कोई ऑर्डर नहीं मिला</h6>
                            <a href="../order.php" class="btn btn-primary mt-2">
                                <i class="fas fa-plus me-2"></i>पहला ऑर्डर करें
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>ऑर्डर #</th>
                                        <th>पैकेज</th>
                                        <th>राशि</th>
                                        <th>स्टेटस</th>
                                        <th>दिनांक</th>
                                        <th>एक्शन</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_orders as $order): ?>
                                    <tr>
                                        <td><strong><?php echo $order['order_number']; ?></strong></td>
                                        <td><?php echo $order['package_name']; ?></td>
                                        <td>₹<?php echo number_format($order['total_amount']); ?></td>
                                        <td><?php echo get_order_status_badge($order['status']); ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($order['created_at'])); ?></td>
                                        <td>
                                            <a href="order-details.php?id=<?php echo $order['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Profile Summary -->
            <div class="col-lg-4 mb-4">
                <div class="admin-card slide-in-admin">
                    <div class="card-header">
                        <h5 class="mb-0">प्रोफाइल सारांश</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="profile-avatar mb-3">
                            <div class="avatar-circle mx-auto" style="width: 80px; height: 80px; font-size: 2rem;">
                                <?php echo strtoupper(substr($user_profile['full_name'], 0, 2)); ?>
                            </div>
                        </div>
                        <h6 class="mb-1"><?php echo $user_profile['full_name']; ?></h6>
                        <p class="text-muted mb-3"><?php echo $user_profile['email']; ?></p>
                        
                        <div class="profile-stats">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="stat-item">
                                        <h6 class="text-primary mb-0"><?php echo $stats['total_orders'] ?? 0; ?></h6>
                                        <small class="text-muted">ऑर्डर</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-item">
                                        <h6 class="text-success mb-0"><?php echo $stats['completed_orders'] ?? 0; ?></h6>
                                        <small class="text-muted">पूर्ण</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <a href="profile.php" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit me-2"></i>प्रोफाइल संपादित करें
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Support Card -->
                <div class="admin-card slide-in-admin mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">सहायता चाहिए?</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">हमारी टीम आपकी सहायता के लिए तैयार है।</p>
                        <div class="d-grid gap-2">
                            <a href="../contact.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-envelope me-2"></i>संपर्क करें
                            </a>
                            <a href="https://wa.me/919876543210" class="btn btn-outline-success btn-sm" target="_blank">
                                <i class="fab fa-whatsapp me-2"></i>WhatsApp
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/enhanced.js"></script>
    
    <script>
        // Initialize counter animations
        document.addEventListener('DOMContentLoaded', function() {
            animateCounters();
        });

        function animateCounters() {
            const counters = document.querySelectorAll('[data-counter]');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-counter'));
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target.toLocaleString('hi-IN');
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current).toLocaleString('hi-IN');
                    }
                }, 20);
            });
        }
    </script>
    
    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--admin-primary), #0056b3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        .profile-stats .stat-item {
            padding: 0.5rem;
        }
    </style>
</body>
</html>
