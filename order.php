<?php
require_once 'config/config.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('login.php');
}

$error = '';
$success = '';
$package_id = isset($_GET['package']) ? (int)$_GET['package'] : 0;

// Get package details
$package = null;
if ($package_id) {
    $package = $db->fetchOne("SELECT * FROM service_packages WHERE id = ? AND is_active = 1", [$package_id]);
    if (!$package) {
        redirect('services.php');
    }
}

// Handle order form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $package_id = (int)($_POST['package_id'] ?? 0);
    $requirements = sanitize_input($_POST['requirements'] ?? '');
    $csrf_token = $_POST['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!verify_csrf_token($csrf_token)) {
        $error = 'सुरक्षा त्रुटि। कृपया पुनः प्रयास करें।';
    } elseif (!$package_id) {
        $error = 'कृपया एक पैकेज चुनें।';
    } elseif (empty($requirements)) {
        $error = 'कृपया अपनी आवश्यकताएं बताएं।';
    } else {
        try {
            // Get package details
            $package = $db->fetchOne("SELECT * FROM service_packages WHERE id = ? AND is_active = 1", [$package_id]);
            
            if (!$package) {
                $error = 'अमान्य पैकेज।';
            } else {
                // Generate order number
                $order_number = generate_order_number();
                
                // Create order
                $order_id = $db->insert('orders', [
                    'order_number' => $order_number,
                    'user_id' => get_current_user_id(),
                    'package_id' => $package_id,
                    'status' => 'pending',
                    'total_amount' => $package['price'],
                    'currency' => 'INR',
                    'payment_status' => 'pending',
                    'requirements' => $requirements,
                    'estimated_delivery' => date('Y-m-d', strtotime('+30 days'))
                ]);
                
                if ($order_id) {
                    // Log activity
                    log_activity(get_current_user_id(), 'order_created', "Order {$order_number} created");
                    
                    // Add order status history
                    $db->insert('order_status_history', [
                        'order_id' => $order_id,
                        'new_status' => 'pending',
                        'changed_by' => get_current_user_id(),
                        'notes' => 'Order created'
                    ]);
                    
                    // Send order confirmation email
                    // send_email(...);
                    
                    if ($package['price'] > 0) {
                        // Redirect to payment
                        redirect("payment.php?order={$order_number}");
                    } else {
                        // Custom development - redirect to dashboard
                        redirect("client/dashboard.php?order_success=1");
                    }
                } else {
                    $error = 'ऑर्डर बनाने में त्रुटि। कृपया बाद में प्रयास करें।';
                }
            }
        } catch (Exception $e) {
            error_log("Order creation error: " . $e->getMessage());
            $error = 'ऑर्डर बनाने में त्रुटि। कृपया बाद में प्रयास करें।';
        }
    }
}

// Get all packages for selection
$packages = $db->fetchAll("SELECT * FROM service_packages WHERE is_active = 1 ORDER BY display_order");
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ऑर्डर करें - <?php echo SITE_NAME_HINDI; ?></title>
    <meta name="description" content="अपना ऑर्डर दें">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">होम</a>
                <a class="nav-link" href="client/dashboard.php">डैशबोर्ड</a>
                <a class="nav-link" href="logout.php">लॉगआउट</a>
            </div>
        </div>
    </nav>

    <!-- Order Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-lg border-0">
                        <div class="card-header bg-primary text-white">
                            <h3 class="mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>नया ऑर्डर
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <?php endif; ?>

                            <form method="POST" action="order.php" id="orderForm">
                                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                
                                <div class="mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="fas fa-box me-1"></i>पैकेज चुनें *
                                    </label>
                                    <div class="row g-3">
                                        <?php foreach ($packages as $pkg): ?>
                                        <div class="col-md-6">
                                            <div class="package-option">
                                                <input type="radio" class="btn-check" name="package_id" 
                                                       id="package_<?php echo $pkg['id']; ?>" 
                                                       value="<?php echo $pkg['id']; ?>"
                                                       <?php echo ($package && $package['id'] == $pkg['id']) ? 'checked' : ''; ?>>
                                                <label class="btn btn-outline-primary w-100 h-100 p-3" 
                                                       for="package_<?php echo $pkg['id']; ?>">
                                                    <div class="text-start">
                                                        <h5 class="mb-2"><?php echo $pkg['name_hindi']; ?></h5>
                                                        <p class="mb-2 text-muted small"><?php echo $pkg['description_hindi']; ?></p>
                                                        <div class="fw-bold text-primary">
                                                            <?php if ($pkg['price'] > 0): ?>
                                                                <?php echo format_currency($pkg['price']); ?>
                                                            <?php else: ?>
                                                                मूल्य पर अनुरोध
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="requirements" class="form-label fw-bold">
                                        <i class="fas fa-list me-1"></i>आपकी आवश्यकताएं *
                                    </label>
                                    <textarea class="form-control" id="requirements" name="requirements" 
                                              rows="6" required 
                                              placeholder="कृपया अपनी वेबसाइट की आवश्यकताओं का विस्तार से वर्णन करें:&#10;&#10;- वेबसाइट का प्रकार&#10;- मुख्य सुविधाएं&#10;- डिजाइन प्राथमिकताएं&#10;- कोई विशेष आवश्यकताएं&#10;- लक्षित दर्शक&#10;- समयसीमा की अपेक्षाएं"><?php echo htmlspecialchars($requirements ?? ''); ?></textarea>
                                    <div class="form-text">
                                        जितनी अधिक जानकारी आप देंगे, उतना बेहतर हम आपकी आवश्यकताओं को समझ सकेंगे।
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>ऑर्डर सबमिट करें
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Order Summary -->
                    <div class="card shadow border-0 mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>ऑर्डर प्रक्रिया
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="step-item">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h6>पैकेज चुनें</h6>
                                    <p class="text-muted small">अपनी आवश्यकताओं के अनुसार पैकेज चुनें</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h6>आवश्यकताएं बताएं</h6>
                                    <p class="text-muted small">अपनी वेबसाइट की जरूरतों का विवरण दें</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h6>भुगतान करें</h6>
                                    <p class="text-muted small">सुरक्षित Razorpay के माध्यम से भुगतान</p>
                                </div>
                            </div>
                            <div class="step-item">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h6>काम शुरू</h6>
                                    <p class="text-muted small">हम तुरंत आपके प्रोजेक्ट पर काम शुरू करेंगे</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact Support -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-headset me-2"></i>सहायता चाहिए?
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">कोई सवाल है? हमसे संपर्क करें:</p>
                            <div class="d-grid gap-2">
                                <a href="https://wa.me/919876543210" class="btn btn-success btn-sm" target="_blank">
                                    <i class="fab fa-whatsapp me-2"></i>WhatsApp चैट
                                </a>
                                <a href="tel:+919876543210" class="btn btn-primary btn-sm">
                                    <i class="fas fa-phone me-2"></i>फोन करें
                                </a>
                                <a href="mailto:<EMAIL>" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-envelope me-2"></i>ईमेल भेजें
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        document.getElementById('orderForm').addEventListener('submit', function(e) {
            const packageSelected = document.querySelector('input[name="package_id"]:checked');
            const requirements = document.getElementById('requirements').value.trim();
            
            if (!packageSelected) {
                e.preventDefault();
                alert('कृपया एक पैकेज चुनें।');
                return false;
            }
            
            if (!requirements) {
                e.preventDefault();
                alert('कृपया अपनी आवश्यकताएं बताएं।');
                return false;
            }
            
            if (requirements.length < 50) {
                e.preventDefault();
                alert('कृपया अपनी आवश्यकताओं का विस्तार से वर्णन करें (कम से कम 50 अक्षर)।');
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="loading me-2"></span>ऑर्डर सबमिट हो रहा है...';
            submitBtn.disabled = true;
        });

        // Package selection styling
        document.querySelectorAll('input[name="package_id"]').forEach(function(radio) {
            radio.addEventListener('change', function() {
                // Remove active class from all labels
                document.querySelectorAll('.package-option label').forEach(function(label) {
                    label.classList.remove('active');
                });
                
                // Add active class to selected label
                if (this.checked) {
                    this.nextElementSibling.classList.add('active');
                }
            });
        });
    </script>
    
    <style>
        .package-option label {
            transition: all 0.3s ease;
            border: 2px solid #dee2e6;
        }
        
        .package-option label:hover {
            border-color: var(--bs-primary);
            transform: translateY(-2px);
        }
        
        .package-option label.active {
            border-color: var(--bs-primary);
            background-color: rgba(13, 110, 253, 0.1);
        }
        
        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .step-number {
            width: 30px;
            height: 30px;
            background: var(--bs-primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .step-content h6 {
            margin-bottom: 0.25rem;
        }
    </style>
</body>
</html>
