<?php
require_once 'config/config.php';

// Check if user is logged in
if (is_logged_in()) {
    $user_id = get_current_user_id();
    
    // Log activity
    log_activity($user_id, 'logout', 'User logged out');
    
    // Destroy session
    session_destroy();
    
    // Redirect to login page with logout message
    redirect('login.php?logout=1');
} else {
    // User not logged in, redirect to home
    redirect('index.php');
}
?>
