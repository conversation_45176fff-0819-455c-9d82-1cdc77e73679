/* Custom CSS for Website Developer Services */

/* Font Family */
body {
    font-family: 'Noto Sans Devanagari', sans-serif;
    line-height: 1.6;
}

/* Custom Colors */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --purple-color: #6f42c1;
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--warning-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    padding-top: 100px;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.feature-icon {
    margin-bottom: 1rem;
}

/* Package Cards */
.package-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.package-card.featured {
    border-color: var(--warning-color);
    transform: scale(1.05);
}

.package-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.package-badge {
    position: absolute;
    top: 20px;
    right: -30px;
    background: var(--warning-color);
    color: var(--dark-color);
    padding: 5px 40px;
    font-weight: bold;
    font-size: 0.8rem;
    transform: rotate(45deg);
    z-index: 1;
}

.package-header {
    border-bottom: 1px solid #e9ecef;
}

.package-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.package-price {
    margin-bottom: 0;
}

.price-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    display: block;
}

.price-period {
    font-size: 1rem;
    color: var(--secondary-color);
    font-weight: 500;
}

.package-description {
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
}

.package-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
}

.package-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.package-features li:last-child {
    border-bottom: none;
}

/* Buttons */
.btn {
    font-weight: 600;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, var(--primary-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.4);
}

.btn-warning {
    background: var(--warning-color);
    border: none;
    color: var(--dark-color);
}

.btn-warning:hover {
    background: #e0a800;
    color: var(--dark-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Cards */
.card {
    border-radius: 15px;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
    font-weight: 600;
}

/* Tables */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.table thead th {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

/* Social Links */
.social-links a {
    transition: all 0.3s ease;
}

.social-links a:hover {
    color: var(--warning-color) !important;
    transform: translateY(-2px);
}

/* Utilities */
.text-purple {
    color: var(--purple-color) !important;
}

.bg-purple {
    background-color: var(--purple-color) !important;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-section {
        padding-top: 80px;
        text-align: center;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .package-card.featured {
        transform: none;
    }
    
    .package-card.featured:hover {
        transform: translateY(-10px);
    }
    
    .package-badge {
        font-size: 0.7rem;
        padding: 3px 30px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Dashboard Styles */
.dashboard-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.dashboard-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.dashboard-stat {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.dashboard-label {
    color: var(--secondary-color);
    font-weight: 500;
}

/* Blog Styles */
.blog-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.blog-image {
    height: 200px;
    object-fit: cover;
}

.blog-meta {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.blog-title {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 600;
}

.blog-title:hover {
    color: var(--primary-color);
}

/* Contact Form */
.contact-form {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Footer */
footer {
    margin-top: auto;
}

footer h5, footer h6 {
    color: white;
}

footer .text-muted {
    color: #adb5bd !important;
}

footer .text-muted:hover {
    color: var(--warning-color) !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Print Styles */
@media print {
    .navbar, .footer, .btn {
        display: none !important;
    }
}
