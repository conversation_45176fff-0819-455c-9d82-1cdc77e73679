<?php
require_once '../config/config.php';

// Check if user is logged in and is a client
if (!is_logged_in()) {
    redirect('../login.php');
}

if (is_admin()) {
    redirect('../admin/dashboard.php');
}

$user_id = get_current_user_id();

// Get user's orders
$orders = $db->fetchAll(
    "SELECT o.*, sp.name_hindi as package_name, sp.price as package_price 
     FROM orders o 
     JOIN service_packages sp ON o.package_id = sp.id 
     WHERE o.user_id = ? 
     ORDER BY o.created_at DESC",
    [$user_id]
);

// Get order statistics
$stats = $db->fetchOne(
    "SELECT 
        COUNT(*) as total_orders,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_orders,
        SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as total_paid
     FROM orders 
     WHERE user_id = ?",
    [$user_id]
);

$success_message = '';
if (isset($_GET['order_success'])) {
    $success_message = 'आपका ऑर्डर सफलतापूर्वक सबमिट हो गया है। हम जल्द ही आपसे संपर्क करेंगे।';
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>क्लाइंट डैशबोर्ड - <?php echo SITE_NAME_HINDI; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">

    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="../index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>डैशबोर्ड
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-1"></i>मेरे ऑर्डर
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-1"></i>प्रोफाइल
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i><?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../index.php">होम पेज</a></li>
                            <li><a class="dropdown-item" href="profile.php">प्रोफाइल</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">लॉगआउट</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-2">नमस्कार, <?php echo $_SESSION['user_name']; ?>!</h2>
                                <p class="mb-0">आपके डैशबोर्ड में आपका स्वागत है। यहाँ आप अपने सभी ऑर्डर और प्रोजेक्ट की स्थिति देख सकते हैं।</p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <a href="../order.php" class="btn btn-warning btn-lg">
                                    <i class="fas fa-plus me-2"></i>नया ऑर्डर
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="dashboard-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="dashboard-stat"><?php echo $stats['total_orders'] ?? 0; ?></div>
                            <div class="dashboard-label">कुल ऑर्डर</div>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="dashboard-stat"><?php echo $stats['in_progress_orders'] ?? 0; ?></div>
                            <div class="dashboard-label">प्रगति में</div>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="dashboard-stat"><?php echo $stats['completed_orders'] ?? 0; ?></div>
                            <div class="dashboard-label">पूर्ण</div>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="dashboard-card">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <div class="dashboard-stat"><?php echo format_currency($stats['total_paid'] ?? 0); ?></div>
                            <div class="dashboard-label">कुल भुगतान</div>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-rupee-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow border-0">
                    <div class="card-header bg-white border-bottom">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>हाल के ऑर्डर
                                </h5>
                            </div>
                            <div class="col-auto">
                                <a href="orders.php" class="btn btn-outline-primary btn-sm">
                                    सभी ऑर्डर देखें
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($orders)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">कोई ऑर्डर नहीं मिला</h5>
                            <p class="text-muted">अभी तक आपने कोई ऑर्डर नहीं दिया है।</p>
                            <a href="../order.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>पहला ऑर्डर दें
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>ऑर्डर नंबर</th>
                                        <th>पैकेज</th>
                                        <th>राशि</th>
                                        <th>स्थिति</th>
                                        <th>भुगतान</th>
                                        <th>दिनांक</th>
                                        <th>कार्य</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($orders, 0, 5) as $order): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $order['order_number']; ?></strong>
                                        </td>
                                        <td><?php echo $order['package_name']; ?></td>
                                        <td><?php echo format_currency($order['total_amount']); ?></td>
                                        <td><?php echo get_order_status_badge($order['status']); ?></td>
                                        <td><?php echo get_payment_status_badge($order['payment_status']); ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($order['created_at'])); ?></td>
                                        <td>
                                            <a href="order-details.php?id=<?php echo $order['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($order['payment_status'] === 'pending' && $order['total_amount'] > 0): ?>
                                            <a href="../payment.php?order=<?php echo $order['order_number']; ?>" 
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-credit-card"></i>
                                            </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle fa-3x text-primary mb-3"></i>
                        <h5>नया प्रोजेक्ट शुरू करें</h5>
                        <p class="text-muted">अपनी अगली वेबसाइट के लिए ऑर्डर दें</p>
                        <a href="../order.php" class="btn btn-primary">
                            ऑर्डर करें
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-headset fa-3x text-success mb-3"></i>
                        <h5>सहायता चाहिए?</h5>
                        <p class="text-muted">हमारी टीम से संपर्क करें</p>
                        <a href="../contact.php" class="btn btn-success">
                            संपर्क करें
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-refresh dashboard every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
