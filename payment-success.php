<?php
require_once 'config/config.php';

// Check if user is logged in
if (!is_logged_in()) {
    redirect('login.php');
}

$error = '';
$success = '';

// Handle payment success
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $razorpay_payment_id = $_POST['razorpay_payment_id'] ?? '';
    $razorpay_order_id = $_POST['razorpay_order_id'] ?? '';
    $razorpay_signature = $_POST['razorpay_signature'] ?? '';
    $order_number = $_POST['order_number'] ?? '';
    
    if (empty($razorpay_payment_id) || empty($order_number)) {
        redirect('client/dashboard.php');
    }
    
    try {
        // Get order details
        $order = $db->fetchOne(
            "SELECT * FROM orders WHERE order_number = ? AND user_id = ?",
            [$order_number, get_current_user_id()]
        );
        
        if (!$order) {
            redirect('client/dashboard.php');
        }
        
        // In real implementation, verify payment signature with <PERSON>zorpay
        // For demo, we'll assume payment is successful
        
        // Update order payment status
        $db->update('orders', [
            'payment_status' => 'paid',
            'razorpay_payment_id' => $razorpay_payment_id,
            'status' => 'confirmed'
        ], 'id = ?', [$order['id']]);
        
        // Insert payment record
        $db->insert('payments', [
            'order_id' => $order['id'],
            'razorpay_payment_id' => $razorpay_payment_id,
            'razorpay_order_id' => $razorpay_order_id,
            'amount' => $order['total_amount'],
            'currency' => 'INR',
            'status' => 'captured',
            'method' => 'razorpay'
        ]);
        
        // Add order status history
        $db->insert('order_status_history', [
            'order_id' => $order['id'],
            'old_status' => 'pending',
            'new_status' => 'confirmed',
            'changed_by' => get_current_user_id(),
            'notes' => 'Payment successful'
        ]);
        
        // Log activity
        log_activity(get_current_user_id(), 'payment_success', "Payment successful for order {$order_number}");
        
        // Send confirmation email (implement later)
        // send_email(...);
        
        $success = true;
        
    } catch (Exception $e) {
        error_log("Payment processing error: " . $e->getMessage());
        $error = 'भुगतान प्रोसेसिंग में त्रुटि। कृपया सहायता से संपर्क करें।';
    }
} else {
    redirect('client/dashboard.php');
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>भुगतान सफल - <?php echo SITE_NAME_HINDI; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="client/dashboard.php">डैशबोर्ड</a>
                <a class="nav-link" href="logout.php">लॉगआउट</a>
            </div>
        </div>
    </nav>

    <!-- Success/Error Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <?php if ($success): ?>
                    <div class="card shadow-lg border-0 text-center">
                        <div class="card-body p-5">
                            <div class="success-icon mb-4">
                                <i class="fas fa-check-circle fa-5x text-success"></i>
                            </div>
                            <h2 class="text-success mb-3">भुगतान सफल!</h2>
                            <p class="lead mb-4">
                                आपका भुगतान सफलतापूर्वक पूरा हो गया है। हमारी टीम जल्द ही आपके प्रोजेक्ट पर काम शुरू करेगी।
                            </p>
                            
                            <div class="payment-details bg-light p-3 rounded mb-4">
                                <h5 class="mb-3">भुगतान विवरण</h5>
                                <div class="row text-start">
                                    <div class="col-6">
                                        <strong>ऑर्डर नंबर:</strong><br>
                                        <?php echo $order['order_number']; ?>
                                    </div>
                                    <div class="col-6">
                                        <strong>भुगतान ID:</strong><br>
                                        <?php echo $razorpay_payment_id; ?>
                                    </div>
                                    <div class="col-6 mt-2">
                                        <strong>राशि:</strong><br>
                                        <?php echo format_currency($order['total_amount']); ?>
                                    </div>
                                    <div class="col-6 mt-2">
                                        <strong>दिनांक:</strong><br>
                                        <?php echo date('d/m/Y H:i'); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="client/dashboard.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-tachometer-alt me-2"></i>डैशबोर्ड पर जाएं
                                </a>
                                <a href="client/order-details.php?id=<?php echo $order['id']; ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-2"></i>ऑर्डर विवरण देखें
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Next Steps -->
                    <div class="card mt-4 border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-list-ol me-2"></i>अगले चरण
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="step-timeline">
                                <div class="step-item completed">
                                    <div class="step-marker"></div>
                                    <div class="step-content">
                                        <h6>ऑर्डर दिया गया</h6>
                                        <small class="text-muted">पूर्ण</small>
                                    </div>
                                </div>
                                <div class="step-item completed">
                                    <div class="step-marker"></div>
                                    <div class="step-content">
                                        <h6>भुगतान सफल</h6>
                                        <small class="text-muted">पूर्ण</small>
                                    </div>
                                </div>
                                <div class="step-item active">
                                    <div class="step-marker"></div>
                                    <div class="step-content">
                                        <h6>प्रोजेक्ट शुरू</h6>
                                        <small class="text-muted">24 घंटे में</small>
                                    </div>
                                </div>
                                <div class="step-item">
                                    <div class="step-marker"></div>
                                    <div class="step-content">
                                        <h6>डिलीवरी</h6>
                                        <small class="text-muted">30 दिन में</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php else: ?>
                    <div class="card shadow-lg border-0 text-center">
                        <div class="card-body p-5">
                            <div class="error-icon mb-4">
                                <i class="fas fa-times-circle fa-5x text-danger"></i>
                            </div>
                            <h2 class="text-danger mb-3">भुगतान में त्रुटि!</h2>
                            <p class="lead mb-4"><?php echo $error; ?></p>
                            
                            <div class="d-grid gap-2">
                                <a href="payment.php?order=<?php echo $order_number; ?>" class="btn btn-primary btn-lg">
                                    <i class="fas fa-redo me-2"></i>पुनः प्रयास करें
                                </a>
                                <a href="client/dashboard.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-home me-2"></i>डैशबोर्ड पर जाएं
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Support -->
                    <div class="card mt-4 border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-headset me-2"></i>सहायता चाहिए?
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">यदि आपको कोई समस्या है या कोई प्रश्न है, तो हमसे संपर्क करें:</p>
                            <div class="row">
                                <div class="col-md-4">
                                    <a href="https://wa.me/919876543210" class="btn btn-success btn-sm w-100 mb-2" target="_blank">
                                        <i class="fab fa-whatsapp me-1"></i>WhatsApp
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="tel:+919876543210" class="btn btn-primary btn-sm w-100 mb-2">
                                        <i class="fas fa-phone me-1"></i>कॉल करें
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="mailto:<EMAIL>" class="btn btn-secondary btn-sm w-100 mb-2">
                                        <i class="fas fa-envelope me-1"></i>ईमेल
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        .success-icon, .error-icon {
            animation: bounceIn 0.6s ease-out;
        }
        
        @keyframes bounceIn {
            0% {
                transform: scale(0.3);
                opacity: 0;
            }
            50% {
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .step-timeline {
            position: relative;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            position: relative;
        }
        
        .step-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 12px;
            top: 25px;
            width: 2px;
            height: 30px;
            background: #dee2e6;
        }
        
        .step-item.completed::after {
            background: #198754;
        }
        
        .step-item.active::after {
            background: #ffc107;
        }
        
        .step-marker {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #dee2e6;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .step-item.completed .step-marker {
            background: #198754;
        }
        
        .step-item.active .step-marker {
            background: #ffc107;
        }
        
        .step-content h6 {
            margin-bottom: 0.25rem;
        }
    </style>
</body>
</html>
