<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!is_logged_in()) {
    redirect('../login.php');
}

if (!is_admin()) {
    redirect('../client/dashboard.php');
}

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_general'])) {
        try {
            // Update general settings
            $settings = [
                'site_name_english' => $_POST['site_name_english'],
                'site_name_hindi' => $_POST['site_name_hindi'],
                'site_description' => $_POST['site_description'],
                'contact_email' => $_POST['contact_email'],
                'contact_phone' => $_POST['contact_phone'],
                'whatsapp_number' => $_POST['whatsapp_number'],
                'address' => $_POST['address'],
                'facebook_url' => $_POST['facebook_url'],
                'twitter_url' => $_POST['twitter_url'],
                'linkedin_url' => $_POST['linkedin_url'],
                'instagram_url' => $_POST['instagram_url']
            ];
            
            foreach ($settings as $key => $value) {
                $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);
                if ($existing) {
                    $db->update('site_settings', ['setting_value' => $value], 'setting_key = ?', [$key]);
                } else {
                    $db->insert('site_settings', [
                        'setting_key' => $key,
                        'setting_value' => $value
                    ]);
                }
            }
            
            $success = 'सामान्य सेटिंग्स सफलतापूर्वक अपडेट हुईं।';
        } catch (Exception $e) {
            $error = 'सेटिंग्स अपडेट करने में त्रुटि।';
        }
    }
    
    if (isset($_POST['update_payment'])) {
        try {
            // Update payment settings
            $payment_settings = [
                'razorpay_key_id' => $_POST['razorpay_key_id'],
                'razorpay_key_secret' => $_POST['razorpay_key_secret'],
                'payment_mode' => $_POST['payment_mode'], // test or live
                'currency' => $_POST['currency'],
                'tax_percentage' => $_POST['tax_percentage']
            ];
            
            foreach ($payment_settings as $key => $value) {
                $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);
                if ($existing) {
                    $db->update('site_settings', ['setting_value' => $value], 'setting_key = ?', [$key]);
                } else {
                    $db->insert('site_settings', [
                        'setting_key' => $key,
                        'setting_value' => $value
                    ]);
                }
            }
            
            $success = 'पेमेंट सेटिंग्स सफलतापूर्वक अपडेट हुईं।';
        } catch (Exception $e) {
            $error = 'पेमेंट सेटिंग्स अपडेट करने में त्रुटि।';
        }
    }
}

// Get current settings
$current_settings = [];
$settings_data = $db->fetchAll("SELECT setting_key, setting_value FROM site_settings");
foreach ($settings_data as $setting) {
    $current_settings[$setting['setting_key']] = $setting['setting_value'];
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>सेटिंग्स - <?php echo SITE_NAME_HINDI; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/admin.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="../index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>डैशबोर्ड
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-1"></i>ऑर्डर
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.php">
                            <i class="fas fa-users me-1"></i>क्लाइंट
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">
                            <i class="fas fa-cogs me-1"></i>सेवाएं
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i><?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../index.php">होम पेज</a></li>
                            <li><a class="dropdown-item active" href="settings.php">सेटिंग्स</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">लॉगआउट</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Success/Error Messages -->
        <?php if (isset($success)): ?>
        <div class="alert alert-success admin-alert admin-alert-success fade-in-admin">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
        </div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
        <div class="alert alert-danger admin-alert admin-alert-danger fade-in-admin">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
        </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="admin-page-header fade-in-admin">
            <div>
                <h1 class="admin-page-title">सिस्टम सेटिंग्स</h1>
                <p class="admin-page-subtitle">वेबसाइट और सिस्टम कॉन्फ़िगरेशन मैनेज करें</p>
            </div>
        </div>

        <!-- Settings Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="admin-card slide-in-admin">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" 
                                        data-bs-target="#general" type="button" role="tab">
                                    <i class="fas fa-cog me-2"></i>सामान्य सेटिंग्स
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="payment-tab" data-bs-toggle="tab" 
                                        data-bs-target="#payment" type="button" role="tab">
                                    <i class="fas fa-credit-card me-2"></i>पेमेंट सेटिंग्स
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="email-tab" data-bs-toggle="tab" 
                                        data-bs-target="#email" type="button" role="tab">
                                    <i class="fas fa-envelope me-2"></i>ईमेल सेटिंग्स
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="backup-tab" data-bs-toggle="tab" 
                                        data-bs-target="#backup" type="button" role="tab">
                                    <i class="fas fa-database me-2"></i>बैकअप
                                </button>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="card-body">
                        <div class="tab-content" id="settingsTabContent">
                            <!-- General Settings -->
                            <div class="tab-pane fade show active" id="general" role="tabpanel">
                                <form method="POST" class="enhanced-form">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">साइट नाम (English)</label>
                                                <input type="text" name="site_name_english" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['site_name_english'] ?? ''; ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">साइट नाम (हिंदी)</label>
                                                <input type="text" name="site_name_hindi" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['site_name_hindi'] ?? ''; ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="admin-form-group">
                                        <label class="admin-form-label">साइट विवरण</label>
                                        <textarea name="site_description" class="form-control admin-form-control" rows="3"><?php echo $current_settings['site_description'] ?? ''; ?></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">संपर्क ईमेल</label>
                                                <input type="email" name="contact_email" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['contact_email'] ?? ''; ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">संपर्क फोन</label>
                                                <input type="tel" name="contact_phone" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['contact_phone'] ?? ''; ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">WhatsApp नंबर</label>
                                                <input type="tel" name="whatsapp_number" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['whatsapp_number'] ?? ''; ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">पता</label>
                                                <input type="text" name="address" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['address'] ?? ''; ?>">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <h6 class="mt-4 mb-3">सोशल मीडिया लिंक</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">Facebook URL</label>
                                                <input type="url" name="facebook_url" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['facebook_url'] ?? ''; ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">Twitter URL</label>
                                                <input type="url" name="twitter_url" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['twitter_url'] ?? ''; ?>">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">LinkedIn URL</label>
                                                <input type="url" name="linkedin_url" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['linkedin_url'] ?? ''; ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">Instagram URL</label>
                                                <input type="url" name="instagram_url" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['instagram_url'] ?? ''; ?>">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" name="update_general" class="admin-btn admin-btn-primary">
                                            <i class="fas fa-save me-2"></i>सेव करें
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Payment Settings -->
                            <div class="tab-pane fade" id="payment" role="tabpanel">
                                <form method="POST" class="enhanced-form">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>नोट:</strong> Razorpay API keys को सुरक्षित रखें और केवल authorized personnel के साथ share करें।
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">Razorpay Key ID</label>
                                                <input type="text" name="razorpay_key_id" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['razorpay_key_id'] ?? ''; ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">Razorpay Key Secret</label>
                                                <input type="password" name="razorpay_key_secret" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['razorpay_key_secret'] ?? ''; ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">Payment Mode</label>
                                                <select name="payment_mode" class="form-select admin-form-control" required>
                                                    <option value="test" <?php echo ($current_settings['payment_mode'] ?? '') === 'test' ? 'selected' : ''; ?>>Test Mode</option>
                                                    <option value="live" <?php echo ($current_settings['payment_mode'] ?? '') === 'live' ? 'selected' : ''; ?>>Live Mode</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">Currency</label>
                                                <select name="currency" class="form-select admin-form-control" required>
                                                    <option value="INR" <?php echo ($current_settings['currency'] ?? '') === 'INR' ? 'selected' : ''; ?>>INR (₹)</option>
                                                    <option value="USD" <?php echo ($current_settings['currency'] ?? '') === 'USD' ? 'selected' : ''; ?>>USD ($)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="admin-form-group">
                                                <label class="admin-form-label">Tax Percentage (%)</label>
                                                <input type="number" name="tax_percentage" class="form-control admin-form-control" 
                                                       value="<?php echo $current_settings['tax_percentage'] ?? '18'; ?>" 
                                                       min="0" max="100" step="0.01">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" name="update_payment" class="admin-btn admin-btn-primary">
                                            <i class="fas fa-save me-2"></i>सेव करें
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Email Settings -->
                            <div class="tab-pane fade" id="email" role="tabpanel">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Coming Soon:</strong> ईमेल सेटिंग्स जल्द ही उपलब्ध होंगी।
                                </div>
                            </div>
                            
                            <!-- Backup Settings -->
                            <div class="tab-pane fade" id="backup" role="tabpanel">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Database Backup:</strong> नियमित रूप से अपने डेटाबेस का बैकअप लें।
                                </div>
                                
                                <div class="d-grid gap-2 d-md-block">
                                    <button class="admin-btn admin-btn-success" onclick="createBackup()">
                                        <i class="fas fa-download me-2"></i>बैकअप बनाएं
                                    </button>
                                    <button class="admin-btn admin-btn-warning" onclick="scheduleBackup()">
                                        <i class="fas fa-clock me-2"></i>ऑटो बैकअप सेट करें
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/enhanced.js"></script>
    
    <script>
        function createBackup() {
            if (confirm('क्या आप डेटाबेस का बैकअप बनाना चाहते हैं?')) {
                // Implement backup functionality
                showNotification('बैकअप प्रक्रिया शुरू की गई', 'info');
            }
        }
        
        function scheduleBackup() {
            showNotification('ऑटो बैकअप फीचर जल्द ही उपलब्ध होगा', 'info');
        }
    </script>
</body>
</html>
