<?php
require_once 'config/config.php';

// Get page number for pagination
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 6;
$offset = ($page - 1) * $per_page;

// Get category filter
$category_id = isset($_GET['category']) ? (int)$_GET['category'] : 0;
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';

// Build WHERE clause
$where_conditions = ["bp.status = 'published'"];
$params = [];

if ($category_id > 0) {
    $where_conditions[] = "bp.category_id = ?";
    $params[] = $category_id;
}

if (!empty($search)) {
    $where_conditions[] = "(bp.title LIKE ? OR bp.title_hindi LIKE ? OR bp.content LIKE ? OR bp.content_hindi LIKE ?)";
    $search_term = "%{$search}%";
    $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
}

$where_clause = implode(' AND ', $where_conditions);

// Get total posts count
$total_posts = $db->fetchOne(
    "SELECT COUNT(*) as count FROM blog_posts bp WHERE {$where_clause}",
    $params
)['count'];

$total_pages = ceil($total_posts / $per_page);

// Get blog posts
$posts = $db->fetchAll(
    "SELECT bp.*, bc.name_hindi as category_name, u.full_name as author_name
     FROM blog_posts bp 
     LEFT JOIN blog_categories bc ON bp.category_id = bc.id 
     LEFT JOIN users u ON bp.author_id = u.id 
     WHERE {$where_clause}
     ORDER BY bp.published_at DESC, bp.created_at DESC 
     LIMIT {$per_page} OFFSET {$offset}",
    $params
);

// Get categories for filter
$categories = $db->fetchAll(
    "SELECT * FROM blog_categories WHERE is_active = 1 ORDER BY name_hindi"
);

// Get featured posts
$featured_posts = $db->fetchAll(
    "SELECT bp.*, bc.name_hindi as category_name 
     FROM blog_posts bp 
     LEFT JOIN blog_categories bc ON bp.category_id = bc.id 
     WHERE bp.status = 'published' 
     ORDER BY bp.view_count DESC 
     LIMIT 3"
);
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ब्लॉग - <?php echo SITE_NAME_HINDI; ?></title>
    <meta name="description" content="वेब डेवलपमेंट, डिजिटल मार्केटिंग और तकनीकी जानकारी के लिए हमारा ब्लॉग पढ़ें">
    <meta name="keywords" content="वेब डेवलपमेंट ब्लॉग, डिजिटल मार्केटिंग, SEO टिप्स, तकनीकी लेख">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">होम</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">सेवाएं</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="portfolio.php">पोर्टफोलियो</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">हमारे बारे में</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="blog.php">ब्लॉग</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">संपर्क</a>
                    </li>
                    <?php if (is_logged_in()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>डैशबोर्ड
                            </a>
                            <ul class="dropdown-menu">
                                <?php if (is_admin()): ?>
                                    <li><a class="dropdown-item" href="admin/dashboard.php">एडमिन डैशबोर्ड</a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="client/dashboard.php">क्लाइंट डैशबोर्ड</a></li>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">लॉगआउट</a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">लॉगिन</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-outline-light ms-2 px-3" href="register.php">रजिस्टर करें</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-primary text-white py-5" style="margin-top: 76px;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold mb-3">हमारा ब्लॉग</h1>
                    <p class="lead mb-0">वेब डेवलपमेंट, डिजिटल मार्केटिंग और तकनीकी जानकारी के लिए नवीनतम लेख पढ़ें</p>
                </div>
                <div class="col-lg-4">
                    <!-- Search Form -->
                    <form method="GET" action="blog.php" class="d-flex">
                        <input type="text" class="form-control me-2" name="search" 
                               placeholder="ब्लॉग खोजें..." value="<?php echo htmlspecialchars($search); ?>">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Blog Posts -->
                <div class="col-lg-8">
                    <!-- Category Filter -->
                    <div class="mb-4">
                        <div class="d-flex flex-wrap gap-2">
                            <a href="blog.php" class="btn btn-sm <?php echo $category_id == 0 ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                सभी
                            </a>
                            <?php foreach ($categories as $category): ?>
                            <a href="blog.php?category=<?php echo $category['id']; ?>" 
                               class="btn btn-sm <?php echo $category_id == $category['id'] ? 'btn-primary' : 'btn-outline-primary'; ?>">
                                <?php echo $category['name_hindi']; ?>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Search Results Info -->
                    <?php if (!empty($search) || $category_id > 0): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <?php if (!empty($search)): ?>
                            "<?php echo htmlspecialchars($search); ?>" के लिए <?php echo $total_posts; ?> परिणाम मिले
                        <?php endif; ?>
                        <?php if ($category_id > 0): ?>
                            <?php 
                            $selected_category = array_filter($categories, function($cat) use ($category_id) {
                                return $cat['id'] == $category_id;
                            });
                            $selected_category = reset($selected_category);
                            ?>
                            <?php echo $selected_category['name_hindi']; ?> श्रेणी में <?php echo $total_posts; ?> लेख
                        <?php endif; ?>
                        <a href="blog.php" class="ms-2">सभी देखें</a>
                    </div>
                    <?php endif; ?>

                    <!-- Blog Posts Grid -->
                    <?php if (empty($posts)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">कोई ब्लॉग पोस्ट नहीं मिला</h4>
                        <p class="text-muted">कृपया बाद में फिर से देखें या अन्य श्रेणी चुनें।</p>
                    </div>
                    <?php else: ?>
                    <div class="row g-4">
                        <?php foreach ($posts as $post): ?>
                        <div class="col-md-6">
                            <article class="blog-card h-100">
                                <?php if ($post['featured_image']): ?>
                                <img src="<?php echo $post['featured_image']; ?>" 
                                     alt="<?php echo htmlspecialchars($post['title_hindi']); ?>" 
                                     class="blog-image w-100">
                                <?php else: ?>
                                <div class="blog-image-placeholder bg-light d-flex align-items-center justify-content-center">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                                <?php endif; ?>
                                
                                <div class="p-4">
                                    <div class="blog-meta mb-2">
                                        <?php if ($post['category_name']): ?>
                                        <span class="badge bg-primary me-2"><?php echo $post['category_name']; ?></span>
                                        <?php endif; ?>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo date('d F Y', strtotime($post['published_at'] ?: $post['created_at'])); ?>
                                        </small>
                                    </div>
                                    
                                    <h5 class="blog-title mb-3">
                                        <a href="blog-post.php?slug=<?php echo $post['slug']; ?>" class="text-decoration-none">
                                            <?php echo htmlspecialchars($post['title_hindi'] ?: $post['title']); ?>
                                        </a>
                                    </h5>
                                    
                                    <p class="text-muted mb-3">
                                        <?php 
                                        $excerpt = $post['excerpt_hindi'] ?: $post['excerpt'];
                                        echo htmlspecialchars(substr($excerpt, 0, 120)) . '...';
                                        ?>
                                    </p>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i><?php echo $post['author_name']; ?>
                                        </small>
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i><?php echo $post['view_count']; ?> बार देखा गया
                                        </small>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <a href="blog-post.php?slug=<?php echo $post['slug']; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            पूरा पढ़ें <i class="fas fa-arrow-right ms-1"></i>
                                        </a>
                                    </div>
                                </div>
                            </article>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="Blog pagination" class="mt-5">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="blog.php?page=<?php echo $page-1; ?><?php echo $category_id ? '&category='.$category_id : ''; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>">
                                    <i class="fas fa-chevron-left"></i> पिछला
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="blog.php?page=<?php echo $i; ?><?php echo $category_id ? '&category='.$category_id : ''; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="blog.php?page=<?php echo $page+1; ?><?php echo $category_id ? '&category='.$category_id : ''; ?><?php echo $search ? '&search='.urlencode($search) : ''; ?>">
                                    अगला <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Featured Posts -->
                    <div class="card shadow border-0 mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-star me-2"></i>लोकप्रिय लेख
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($featured_posts as $featured): ?>
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center" 
                                         style="width: 50px; height: 50px;">
                                        <i class="fas fa-blog"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">
                                        <a href="blog-post.php?slug=<?php echo $featured['slug']; ?>" 
                                           class="text-decoration-none">
                                            <?php echo htmlspecialchars(substr($featured['title_hindi'] ?: $featured['title'], 0, 60)); ?>...
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i><?php echo $featured['view_count']; ?> बार देखा गया
                                    </small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Categories -->
                    <div class="card shadow border-0 mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-tags me-2"></i>श्रेणियां
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <?php foreach ($categories as $category): ?>
                                <a href="blog.php?category=<?php echo $category['id']; ?>" 
                                   class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <?php echo $category['name_hindi']; ?>
                                    <span class="badge bg-primary rounded-pill">
                                        <?php 
                                        $count = $db->fetchOne(
                                            "SELECT COUNT(*) as count FROM blog_posts WHERE category_id = ? AND status = 'published'",
                                            [$category['id']]
                                        )['count'];
                                        echo $count;
                                        ?>
                                    </span>
                                </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Newsletter Subscription -->
                    <div class="card shadow border-0 mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-envelope me-2"></i>न्यूज़लेटर
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-3">नवीनतम लेख और अपडेट के लिए सब्स्क्राइब करें</p>
                            <form id="newsletterForm">
                                <div class="mb-3">
                                    <input type="email" class="form-control" placeholder="आपका ईमेल पता" required>
                                </div>
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-paper-plane me-2"></i>सब्स्क्राइब करें
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="fw-bold mb-3"><?php echo SITE_NAME_HINDI; ?></h5>
                    <p class="text-muted">भारतीय बाजार के लिए पेशेवर वेब डेवलपमेंट सेवाएं। आपके व्यवसाय की डिजिटल उपस्थिति बनाने में हमारी मदद लें।</p>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">सेवाएं</h6>
                    <ul class="list-unstyled">
                        <li><a href="services.php" class="text-muted text-decoration-none">वेब डेवलपमेंट</a></li>
                        <li><a href="services.php" class="text-muted text-decoration-none">ई-कॉमर्स</a></li>
                        <li><a href="services.php" class="text-muted text-decoration-none">कस्टम डेवलपमेंट</a></li>
                    </ul>
                </div>
                <div class="col-lg-2">
                    <h6 class="fw-bold mb-3">कंपनी</h6>
                    <ul class="list-unstyled">
                        <li><a href="about.php" class="text-muted text-decoration-none">हमारे बारे में</a></li>
                        <li><a href="blog.php" class="text-muted text-decoration-none">ब्लॉग</a></li>
                        <li><a href="contact.php" class="text-muted text-decoration-none">संपर्क</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h6 class="fw-bold mb-3">संपर्क जानकारी</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li class="mb-2"><i class="fas fa-phone me-2"></i>+91-9876543210</li>
                        <li><i class="fab fa-whatsapp me-2"></i>+91-9876543210</li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p class="mb-0 text-muted">&copy; 2024 <?php echo SITE_NAME_HINDI; ?>. सभी अधिकार सुरक्षित।</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Newsletter subscription
        document.getElementById('newsletterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('धन्यवाद! आपका सब्स्क्रिप्शन सफल रहा।');
        });
    </script>
    
    <style>
        .blog-image-placeholder {
            height: 200px;
        }
    </style>
</body>
</html>
