<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!is_logged_in()) {
    redirect('../login.php');
}

if (!is_admin()) {
    redirect('../client/dashboard.php');
}

// Handle service package operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_package'])) {
        try {
            $db->insert('service_packages', [
                'name_english' => $_POST['name_english'],
                'name_hindi' => $_POST['name_hindi'],
                'description_english' => $_POST['description_english'],
                'description_hindi' => $_POST['description_hindi'],
                'price' => (float)$_POST['price'],
                'features' => json_encode($_POST['features']),
                'is_featured' => isset($_POST['is_featured']) ? 1 : 0,
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'delivery_days' => (int)$_POST['delivery_days']
            ]);
            $success = 'नया सेवा पैकेज सफलतापूर्वक जोड़ा गया।';
        } catch (Exception $e) {
            $error = 'सेवा पैकेज जोड़ने में त्रुटि।';
        }
    }
    
    if (isset($_POST['update_package'])) {
        try {
            $package_id = (int)$_POST['package_id'];
            $db->update('service_packages', [
                'name_english' => $_POST['name_english'],
                'name_hindi' => $_POST['name_hindi'],
                'description_english' => $_POST['description_english'],
                'description_hindi' => $_POST['description_hindi'],
                'price' => (float)$_POST['price'],
                'features' => json_encode($_POST['features']),
                'is_featured' => isset($_POST['is_featured']) ? 1 : 0,
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'delivery_days' => (int)$_POST['delivery_days']
            ], 'id = ?', [$package_id]);
            $success = 'सेवा पैकेज सफलतापूर्वक अपडेट हुआ।';
        } catch (Exception $e) {
            $error = 'सेवा पैकेज अपडेट करने में त्रुटि।';
        }
    }
    
    if (isset($_POST['delete_package'])) {
        try {
            $package_id = (int)$_POST['package_id'];
            $db->delete('service_packages', 'id = ?', [$package_id]);
            $success = 'सेवा पैकेज सफलतापूर्वक डिलीट हुआ।';
        } catch (Exception $e) {
            $error = 'सेवा पैकेज डिलीट करने में त्रुटि।';
        }
    }
}

// Get all service packages
$packages = $db->fetchAll("SELECT * FROM service_packages ORDER BY created_at DESC");

// Get package statistics
$stats = [
    'total_packages' => count($packages),
    'active_packages' => count(array_filter($packages, function($p) { return $p['is_active']; })),
    'featured_packages' => count(array_filter($packages, function($p) { return $p['is_featured']; })),
    'avg_price' => $packages ? array_sum(array_column($packages, 'price')) / count($packages) : 0
];
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>सेवा मैनेजमेंट - <?php echo SITE_NAME_HINDI; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/admin.css" rel="stylesheet">
    
    <!-- Google Fonts for Hindi -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark admin-navbar">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="../index.php">
                <i class="fas fa-code me-2"></i><?php echo SITE_NAME_HINDI; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>डैशबोर्ड
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-1"></i>ऑर्डर
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.php">
                            <i class="fas fa-users me-1"></i>क्लाइंट
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="services.php">
                            <i class="fas fa-cogs me-1"></i>सेवाएं
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i><?php echo $_SESSION['user_name']; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../index.php">होम पेज</a></li>
                            <li><a class="dropdown-item" href="settings.php">सेटिंग्स</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">लॉगआउट</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Success/Error Messages -->
        <?php if (isset($success)): ?>
        <div class="alert alert-success admin-alert admin-alert-success fade-in-admin">
            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
        </div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
        <div class="alert alert-danger admin-alert admin-alert-danger fade-in-admin">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
        </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="admin-page-header fade-in-admin">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="admin-page-title">सेवा मैनेजमेंट</h1>
                    <p class="admin-page-subtitle">सेवा पैकेज देखें और मैनेज करें</p>
                </div>
                <div>
                    <button class="admin-btn admin-btn-primary" data-bs-toggle="modal" data-bs-target="#addPackageModal">
                        <i class="fas fa-plus me-2"></i>नया पैकेज जोड़ें
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['total_packages']; ?></div>
                    <div class="stats-label">कुल पैकेज</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card success fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['active_packages']; ?></div>
                    <div class="stats-label">सक्रिय पैकेज</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card warning fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['featured_packages']; ?></div>
                    <div class="stats-label">फीचर्ड पैकेज</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card info fade-in-admin">
                    <div class="stats-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="stats-number">₹<?php echo number_format($stats['avg_price']); ?></div>
                    <div class="stats-label">औसत मूल्य</div>
                </div>
            </div>
        </div>

        <!-- Service Packages Grid -->
        <div class="row">
            <?php foreach ($packages as $package): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="admin-card slide-in-admin">
                    <?php if ($package['is_featured']): ?>
                    <div class="position-absolute top-0 end-0 m-3">
                        <span class="badge bg-warning text-dark">
                            <i class="fas fa-star me-1"></i>फीचर्ड
                        </span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo $package['name_hindi']; ?></h5>
                        <small class="text-muted"><?php echo $package['name_english']; ?></small>
                    </div>
                    
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <div class="display-6 fw-bold text-primary">₹<?php echo number_format($package['price']); ?></div>
                            <small class="text-muted"><?php echo $package['delivery_days']; ?> दिन में डिलीवरी</small>
                        </div>
                        
                        <p class="text-muted"><?php echo $package['description_hindi']; ?></p>
                        
                        <?php if ($package['features']): ?>
                        <h6>मुख्य सुविधाएं:</h6>
                        <ul class="list-unstyled">
                            <?php foreach (json_decode($package['features'], true) as $feature): ?>
                            <li><i class="fas fa-check text-success me-2"></i><?php echo $feature; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>
                        
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <?php if ($package['is_active']): ?>
                                <span class="badge bg-success">सक्रिय</span>
                                <?php else: ?>
                                <span class="badge bg-secondary">निष्क्रिय</span>
                                <?php endif; ?>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" 
                                        onclick="editPackage(<?php echo htmlspecialchars(json_encode($package)); ?>)">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-outline-danger" 
                                        onclick="deletePackage(<?php echo $package['id']; ?>, '<?php echo $package['name_hindi']; ?>')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            
            <?php if (empty($packages)): ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">कोई सेवा पैकेज नहीं मिला</h5>
                    <button class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#addPackageModal">
                        <i class="fas fa-plus me-2"></i>पहला पैकेज जोड़ें
                    </button>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Package Modal -->
    <div class="modal fade admin-modal" id="addPackageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST" id="addPackageForm">
                    <div class="modal-header">
                        <h5 class="modal-title">नया सेवा पैकेज जोड़ें</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="admin-form-group">
                                    <label class="admin-form-label">नाम (हिंदी)</label>
                                    <input type="text" name="name_hindi" class="form-control admin-form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="admin-form-group">
                                    <label class="admin-form-label">नाम (English)</label>
                                    <input type="text" name="name_english" class="form-control admin-form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="admin-form-group">
                                    <label class="admin-form-label">मूल्य (₹)</label>
                                    <input type="number" name="price" class="form-control admin-form-control" required min="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="admin-form-group">
                                    <label class="admin-form-label">डिलीवरी दिन</label>
                                    <input type="number" name="delivery_days" class="form-control admin-form-control" required min="1">
                                </div>
                            </div>
                        </div>
                        
                        <div class="admin-form-group">
                            <label class="admin-form-label">विवरण (हिंदी)</label>
                            <textarea name="description_hindi" class="form-control admin-form-control" rows="3" required></textarea>
                        </div>
                        
                        <div class="admin-form-group">
                            <label class="admin-form-label">विवरण (English)</label>
                            <textarea name="description_english" class="form-control admin-form-control" rows="3" required></textarea>
                        </div>
                        
                        <div class="admin-form-group">
                            <label class="admin-form-label">सुविधाएं (प्रत्येक लाइन में एक)</label>
                            <textarea id="featuresInput" class="form-control admin-form-control" rows="5" 
                                      placeholder="उदाहरण:&#10;Responsive Design&#10;SEO Optimization&#10;Contact Form"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured">
                                    <label class="form-check-label" for="is_featured">
                                        फीचर्ड पैकेज
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        सक्रिय
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">रद्द करें</button>
                        <button type="submit" name="add_package" class="admin-btn admin-btn-primary">
                            <i class="fas fa-save me-2"></i>सेव करें
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Package Modal -->
    <div class="modal fade admin-modal" id="editPackageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST" id="editPackageForm">
                    <input type="hidden" name="package_id" id="edit_package_id">
                    <div class="modal-header">
                        <h5 class="modal-title">सेवा पैकेज संपादित करें</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Same form fields as add modal -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="admin-form-group">
                                    <label class="admin-form-label">नाम (हिंदी)</label>
                                    <input type="text" name="name_hindi" id="edit_name_hindi" class="form-control admin-form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="admin-form-group">
                                    <label class="admin-form-label">नाम (English)</label>
                                    <input type="text" name="name_english" id="edit_name_english" class="form-control admin-form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="admin-form-group">
                                    <label class="admin-form-label">मूल्य (₹)</label>
                                    <input type="number" name="price" id="edit_price" class="form-control admin-form-control" required min="0" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="admin-form-group">
                                    <label class="admin-form-label">डिलीवरी दिन</label>
                                    <input type="number" name="delivery_days" id="edit_delivery_days" class="form-control admin-form-control" required min="1">
                                </div>
                            </div>
                        </div>
                        
                        <div class="admin-form-group">
                            <label class="admin-form-label">विवरण (हिंदी)</label>
                            <textarea name="description_hindi" id="edit_description_hindi" class="form-control admin-form-control" rows="3" required></textarea>
                        </div>
                        
                        <div class="admin-form-group">
                            <label class="admin-form-label">विवरण (English)</label>
                            <textarea name="description_english" id="edit_description_english" class="form-control admin-form-control" rows="3" required></textarea>
                        </div>
                        
                        <div class="admin-form-group">
                            <label class="admin-form-label">सुविधाएं (प्रत्येक लाइन में एक)</label>
                            <textarea id="edit_featuresInput" class="form-control admin-form-control" rows="5"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_featured" id="edit_is_featured">
                                    <label class="form-check-label" for="edit_is_featured">
                                        फीचर्ड पैकेज
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="edit_is_active">
                                    <label class="form-check-label" for="edit_is_active">
                                        सक्रिय
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">रद्द करें</button>
                        <button type="submit" name="update_package" class="admin-btn admin-btn-primary">
                            <i class="fas fa-save me-2"></i>अपडेट करें
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">पैकेज डिलीट करें</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="package_id" id="delete_package_id">
                        <p>क्या आप वाकई इस पैकेज को डिलीट करना चाहते हैं?</p>
                        <p><strong id="delete_package_name"></strong></p>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            यह एक्शन को वापस नहीं किया जा सकता।
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">रद्द करें</button>
                        <button type="submit" name="delete_package" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>डिलीट करें
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Handle form submissions
        document.getElementById('addPackageForm').addEventListener('submit', function(e) {
            const featuresText = document.getElementById('featuresInput').value;
            const features = featuresText.split('\n').filter(f => f.trim() !== '');
            
            // Create hidden input for features array
            const featuresInput = document.createElement('input');
            featuresInput.type = 'hidden';
            featuresInput.name = 'features[]';
            featuresInput.value = JSON.stringify(features);
            this.appendChild(featuresInput);
        });

        function editPackage(packageData) {
            document.getElementById('edit_package_id').value = packageData.id;
            document.getElementById('edit_name_hindi').value = packageData.name_hindi;
            document.getElementById('edit_name_english').value = packageData.name_english;
            document.getElementById('edit_price').value = packageData.price;
            document.getElementById('edit_delivery_days').value = packageData.delivery_days;
            document.getElementById('edit_description_hindi').value = packageData.description_hindi;
            document.getElementById('edit_description_english').value = packageData.description_english;
            
            // Handle features
            const features = JSON.parse(packageData.features || '[]');
            document.getElementById('edit_featuresInput').value = features.join('\n');
            
            document.getElementById('edit_is_featured').checked = packageData.is_featured == 1;
            document.getElementById('edit_is_active').checked = packageData.is_active == 1;
            
            const modal = new bootstrap.Modal(document.getElementById('editPackageModal'));
            modal.show();
        }

        function deletePackage(packageId, packageName) {
            document.getElementById('delete_package_id').value = packageId;
            document.getElementById('delete_package_name').textContent = packageName;
            
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }

        // Handle edit form submission
        document.getElementById('editPackageForm').addEventListener('submit', function(e) {
            const featuresText = document.getElementById('edit_featuresInput').value;
            const features = featuresText.split('\n').filter(f => f.trim() !== '');
            
            // Create hidden input for features array
            const featuresInput = document.createElement('input');
            featuresInput.type = 'hidden';
            featuresInput.name = 'features[]';
            featuresInput.value = JSON.stringify(features);
            this.appendChild(featuresInput);
        });
    </script>
</body>
</html>
