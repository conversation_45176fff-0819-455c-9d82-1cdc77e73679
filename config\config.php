<?php
/**
 * Main Configuration File
 * Website Developer Services
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Site configuration
define('SITE_URL', 'http://localhost/websitedeveloper0002.in');
define('SITE_NAME', 'Website Developer');
define('SITE_NAME_HINDI', 'वेबसाइट डेवलपर');
define('ADMIN_EMAIL', '<EMAIL>');

// Database configuration
require_once __DIR__ . '/database.php';

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour

// File upload settings
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Email settings
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Website Developer');

// Payment gateway settings
define('RAZORPAY_KEY_ID', '');
define('RAZORPAY_KEY_SECRET', '');

// reCAPTCHA settings
define('RECAPTCHA_SITE_KEY', '');
define('RECAPTCHA_SECRET_KEY', '');

// Google Analytics
define('GA_TRACKING_ID', '');

// Language settings
define('DEFAULT_LANGUAGE', 'hi');
define('SUPPORTED_LANGUAGES', ['hi', 'en']);

// Utility functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generate_csrf_token() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verify_csrf_token($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function redirect($url) {
    header("Location: " . $url);
    exit();
}

function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function is_admin() {
    return is_logged_in() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function get_current_user_id() {
    return isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
}

function format_currency($amount, $currency = 'INR') {
    if ($currency === 'INR') {
        return '₹' . number_format($amount, 2);
    }
    return $currency . ' ' . number_format($amount, 2);
}

function generate_order_number() {
    return 'WD' . date('Ymd') . rand(1000, 9999);
}

function get_order_status_badge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning">लंबित</span>',
        'confirmed' => '<span class="badge bg-info">पुष्ट</span>',
        'in_progress' => '<span class="badge bg-primary">प्रगति में</span>',
        'completed' => '<span class="badge bg-success">पूर्ण</span>',
        'cancelled' => '<span class="badge bg-danger">रद्द</span>',
        'refunded' => '<span class="badge bg-secondary">वापस</span>'
    ];
    return $badges[$status] ?? '<span class="badge bg-secondary">' . $status . '</span>';
}

function get_payment_status_badge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning">लंबित</span>',
        'paid' => '<span class="badge bg-success">भुगतान</span>',
        'failed' => '<span class="badge bg-danger">असफल</span>',
        'refunded' => '<span class="badge bg-secondary">वापस</span>'
    ];
    return $badges[$status] ?? '<span class="badge bg-secondary">' . $status . '</span>';
}

function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'अभी';
    if ($time < 3600) return floor($time/60) . ' मिनट पहले';
    if ($time < 86400) return floor($time/3600) . ' घंटे पहले';
    if ($time < 2592000) return floor($time/86400) . ' दिन पहले';
    if ($time < 31536000) return floor($time/2592000) . ' महीने पहले';
    
    return floor($time/31536000) . ' साल पहले';
}

function send_email($to, $subject, $body, $template_id = null) {
    // This will be implemented with PHPMailer
    // For now, just log the email
    error_log("Email to: $to, Subject: $subject");
    return true;
}

function log_activity($user_id, $action, $details = '') {
    global $db;
    try {
        $db->insert('activity_logs', [
            'user_id' => $user_id,
            'action' => $action,
            'details' => $details,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

// Auto-load classes
spl_autoload_register(function ($class_name) {
    $directories = [
        __DIR__ . '/../classes/',
        __DIR__ . '/../models/',
        __DIR__ . '/../controllers/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $class_name . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Check session timeout
if (is_logged_in()) {
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
        session_destroy();
        redirect(SITE_URL . '/login.php?timeout=1');
    }
    $_SESSION['last_activity'] = time();
}
?>
