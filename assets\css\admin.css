/* Enhanced Admin Dashboard CSS */

:root {
    --admin-primary: #0d6efd;
    --admin-secondary: #6c757d;
    --admin-success: #198754;
    --admin-warning: #ffc107;
    --admin-danger: #dc3545;
    --admin-info: #0dcaf0;
    --admin-light: #f8f9fa;
    --admin-dark: #212529;
    --admin-sidebar: #2c3e50;
    --admin-sidebar-hover: #34495e;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 0.75rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Admin Layout */
.admin-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.admin-navbar {
    background: linear-gradient(135deg, var(--admin-primary), #0056b3) !important;
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(10px);
}

.admin-navbar .navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.admin-navbar .nav-link {
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.admin-navbar .nav-link:hover {
    color: var(--admin-warning) !important;
    transform: translateY(-1px);
}

.admin-navbar .nav-link.active {
    color: var(--admin-warning) !important;
    font-weight: 600;
}

/* Admin Cards */
.admin-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: none;
    transition: var(--transition);
    overflow: hidden;
}

.admin-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.admin-card .card-header {
    background: linear-gradient(135deg, var(--admin-light), white);
    border-bottom: 1px solid rgba(0,0,0,0.1);
    font-weight: 600;
    padding: 1.25rem;
}

.admin-card .card-body {
    padding: 1.5rem;
}

/* Dashboard Stats Cards */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border-left: 4px solid var(--admin-primary);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1), rgba(13, 110, 253, 0.05));
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-8px);
}

.stats-card.success {
    border-left-color: var(--admin-success);
}

.stats-card.warning {
    border-left-color: var(--admin-warning);
}

.stats-card.danger {
    border-left-color: var(--admin-danger);
}

.stats-card.info {
    border-left-color: var(--admin-info);
}

.stats-number {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1;
    background: linear-gradient(135deg, var(--admin-primary), #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--admin-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.5rem;
}

.stats-icon {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--admin-primary), #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    opacity: 0.8;
}

/* Enhanced Tables */
.admin-table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.admin-table .table {
    margin-bottom: 0;
}

.admin-table .table thead th {
    background: linear-gradient(135deg, var(--admin-primary), #0056b3);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.admin-table .table tbody tr {
    transition: var(--transition);
}

.admin-table .table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.01);
}

.admin-table .table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

/* Enhanced Buttons */
.admin-btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    padding: 0.75rem 1.5rem;
}

.admin-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: var(--transition);
}

.admin-btn:hover::before {
    left: 100%;
}

.admin-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.admin-btn-primary {
    background: linear-gradient(135deg, var(--admin-primary), #0056b3);
    border: none;
    color: white;
}

.admin-btn-success {
    background: linear-gradient(135deg, var(--admin-success), #146c43);
    border: none;
    color: white;
}

.admin-btn-warning {
    background: linear-gradient(135deg, var(--admin-warning), #e0a800);
    border: none;
    color: var(--admin-dark);
}

.admin-btn-danger {
    background: linear-gradient(135deg, var(--admin-danger), #b02a37);
    border: none;
    color: white;
}

/* Enhanced Forms */
.admin-form-control {
    border-radius: var(--border-radius);
    border: 2px solid rgba(0,0,0,0.1);
    transition: var(--transition);
    padding: 0.75rem 1rem;
    font-weight: 500;
}

.admin-form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    transform: translateY(-2px);
}

.admin-form-label {
    font-weight: 600;
    color: var(--admin-dark);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.admin-form-group {
    margin-bottom: 1.5rem;
}

/* Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background: rgba(255, 193, 7, 0.2);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.5);
}

.status-confirmed {
    background: rgba(25, 135, 84, 0.2);
    color: #0f5132;
    border: 1px solid rgba(25, 135, 84, 0.5);
}

.status-in-progress {
    background: rgba(13, 202, 240, 0.2);
    color: #055160;
    border: 1px solid rgba(13, 202, 240, 0.5);
}

.status-completed {
    background: rgba(25, 135, 84, 0.2);
    color: #0f5132;
    border: 1px solid rgba(25, 135, 84, 0.5);
}

.status-cancelled {
    background: rgba(220, 53, 69, 0.2);
    color: #58151c;
    border: 1px solid rgba(220, 53, 69, 0.5);
}

/* Payment Status Badges */
.payment-pending {
    background: rgba(255, 193, 7, 0.2);
    color: #856404;
}

.payment-paid {
    background: rgba(25, 135, 84, 0.2);
    color: #0f5132;
}

.payment-failed {
    background: rgba(220, 53, 69, 0.2);
    color: #58151c;
}

.payment-refunded {
    background: rgba(108, 117, 125, 0.2);
    color: #495057;
}

/* Enhanced Modals */
.admin-modal .modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-lg);
}

.admin-modal .modal-header {
    background: linear-gradient(135deg, var(--admin-primary), #0056b3);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
}

.admin-modal .modal-title {
    font-weight: 600;
}

.admin-modal .btn-close {
    filter: invert(1);
}

/* Enhanced Alerts */
.admin-alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.admin-alert-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1), rgba(25, 135, 84, 0.05));
    color: #0f5132;
    border-left: 4px solid var(--admin-success);
}

.admin-alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05));
    color: #856404;
    border-left: 4px solid var(--admin-warning);
}

.admin-alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
    color: #58151c;
    border-left: 4px solid var(--admin-danger);
}

.admin-alert-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1), rgba(13, 202, 240, 0.05));
    color: #055160;
    border-left: 4px solid var(--admin-info);
}

/* Page Header */
.admin-page-header {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border-left: 4px solid var(--admin-primary);
}

.admin-page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-dark);
    margin-bottom: 0.5rem;
}

.admin-page-subtitle {
    color: var(--admin-secondary);
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    margin-bottom: 2rem;
}

.quick-action-btn {
    background: white;
    border: 2px solid var(--admin-primary);
    color: var(--admin-primary);
    border-radius: var(--border-radius);
    padding: 1rem;
    text-align: center;
    transition: var(--transition);
    text-decoration: none;
    display: block;
    font-weight: 600;
}

.quick-action-btn:hover {
    background: var(--admin-primary);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-navbar .navbar-nav {
        background: rgba(255,255,255,0.1);
        border-radius: var(--border-radius);
        margin-top: 1rem;
        padding: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
    }
    
    .stats-number {
        font-size: 2.5rem;
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .admin-page-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .admin-page-title {
        font-size: 1.75rem;
    }
    
    .admin-table {
        font-size: 0.875rem;
    }
    
    .admin-table .table thead th,
    .admin-table .table tbody td {
        padding: 0.75rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .stats-number {
        font-size: 2rem;
    }
    
    .admin-page-title {
        font-size: 1.5rem;
    }
    
    .admin-btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* Loading States */
.admin-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(13, 110, 253, 0.3);
    border-radius: 50%;
    border-top-color: var(--admin-primary);
    animation: adminSpin 1s ease-in-out infinite;
}

@keyframes adminSpin {
    to { transform: rotate(360deg); }
}

/* Animations */
.fade-in-admin {
    animation: fadeInAdmin 0.6s ease-out;
}

@keyframes fadeInAdmin {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-admin {
    animation: slideInAdmin 0.6s ease-out;
}

@keyframes slideInAdmin {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Print Styles */
@media print {
    .admin-navbar,
    .admin-btn,
    .quick-actions {
        display: none !important;
    }
    
    .admin-card,
    .stats-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
    
    .admin-page-header {
        border-left: none !important;
    }
}
